import useTimeDownCounter from "@/hooks/use-time-down-counter";
import { cn } from "@/lib/utils";
import { Dayjs } from "dayjs";
import { HTMLAttributes } from "react";

interface Props extends HTMLAttributes<"span"> {
  targetDate: Dayjs;
}

export default function TimeDownCounter({
  targetDate,
  className,
  ...props
}: Props) {
  const { Hours, Minutes, Seconds } = useTimeDownCounter(targetDate);

  return (
    <span className={cn("text-primary", className)}>
      {`${Hours}:${Minutes}:${Seconds}`}
    </span>
  );
}
