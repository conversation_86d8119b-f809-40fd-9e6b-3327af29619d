{"name": "akal-website", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@next/third-parties": "^15.3.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-toast": "^1.2.6", "@shopify/address": "^4.3.0", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.62.8", "@uidotdev/usehooks": "^2.4.1", "akal-website": "file:", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "dompurify": "^3.2.3", "embla-carousel-react": "^8.5.2", "input-otp": "^1.4.2", "libphonenumber-js": "^1.11.18", "lucide-react": "^0.469.0", "next": "^15.3.3", "next-intl": "^3.26.3", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwind-merge": "^2.5.5", "tailwind-scrollbar": "^3.1.0", "tailwindcss-animate": "^1.0.7", "tanstack": "^1.0.0", "vaul": "^1.1.2", "zod": "^3.24.1", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "^15.3.3", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}