interface Props {
  color?: string;
}
export default function Plant({ color = "#137C44" }: Props) {
  return (
    <svg
      width="23"
      height="20"
      viewBox="0 0 23 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.2391 7.02249C9.98682 6.42654 8.69497 5.85297 7.10099 5.42584C8.01779 5.25295 11.6496 6.71333 11.7454 6.64621C12.5872 6.04619 13.4269 5.58855 14.1291 5.39736C11.237 2.50102 9.60343 2.61899 4.11931 2.36678C1.22514 2.18983 0.362516 1.68744 0.362516 1.68744C0.149986 1.62846 -0.12297 1.80541 0.0603895 2.16135C2.78578 4.76277 1.87732 7.45368 8.02612 10.468C8.68664 9.34729 9.93056 8.04353 11.2391 7.02452V7.02249Z"
        fill={color}
      />
      <path
        d="M9.03489 18.3856C9.03686 18.3797 9.03883 18.3738 9.0408 18.3679C9.50434 17.8436 10.5746 17.2401 11.8738 16.5074C14.1838 15.2046 17.2177 13.4937 18.8526 11.0945C21.2651 7.83407 21.6389 5.59719 22.012 3.36447C22.1845 2.33237 22.3568 1.30117 22.7303 0.170161C22.7678 -0.0962856 22.2906 0.0298191 22.2906 0.0298191C20.9072 1.63989 18.7009 2.90873 16.3738 4.24699C11.8102 6.87146 6.78223 9.76294 6.58628 16.0187C6.57169 17.0092 6.74463 17.6885 7.50308 16.6248C7.61497 16.4674 7.74365 16.2536 7.90249 15.9896C8.81728 14.4692 10.7323 11.2864 16.2023 7.65916C15.8061 8.05026 15.3892 8.44627 14.9611 8.8529C12.6062 11.0896 9.91339 13.6473 8.47822 17.475C8.2177 18.2175 8.03659 18.9543 7.93597 19.665L9.03489 18.3856Z"
        fill={color}
      />
    </svg>
  );
}
