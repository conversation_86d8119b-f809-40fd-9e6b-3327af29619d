"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Heart } from "lucide-react";
import { cn } from "@/lib/utils";
import { ButtonHTMLAttributes } from "react";

interface HeartButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  isLiked?: boolean;
  size?: "sm" | "md" | "lg";
  variant?: "default";
  noBorder?: boolean;
  className?: string;
}

export default function HeartButton({
  isLiked = false,
  size = "md",
  variant = "default",
  noBorder = false,
  className,
  ...props
}: HeartButtonProps) {
  const sizeClasses = {
    sm: {
      button: "w-8 h-8 S:w-9 S:h-9 p-1.5 S:p-2 aspect-square",
      icon: "w-3 h-3 S:w-3.5 S:h-3.5",
    },
    md: {
      button:
        "w-9 h-9 S:w-10 S:h-10 L:w-12 L:h-12 p-2 S:p-2.5 L:p-3 aspect-square",
      icon: "w-4 h-4 S:w-4.5 S:h-4.5 L:w-5 L:h-5",
    },
    lg: {
      button:
        "w-10 h-10 S:w-12 S:h-12 L:w-14 L:h-14 p-2.5 S:p-3 L:p-3.5 aspect-square",
      icon: "w-5 h-5 S:w-5.5 S:h-5.5 L:w-6 L:h-6",
    },
  };

  const variantClasses = {
    default: isLiked
      ? "bg-primary border border-primary"
      : "bg-gray  border border-gray",
  };

  const iconClasses = {
    default: "text-white fill-white",
  };

  return (
    <Button
      variant="ghost"
      className={cn(
        "rounded-full shadow-sm transition-all duration-200 flex items-center justify-center",
        sizeClasses[size].button,
        variantClasses[variant],
        className
      )}
      {...props}
    >
      <Heart
        className={cn(
          "transition-colors duration-200",
          sizeClasses[size].icon,
          iconClasses[variant]
        )}
        strokeWidth={1.5}
      />
    </Button>
  );
}
