import type { Config } from "tailwindcss";

const {
  default: flattenColorPalette,
} = require("tailwindcss/lib/util/flattenColorPalette");

export default {
  darkMode: ["class"],
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/styles/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/modules/catalog/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/modules/cart/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/modules/checkout/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/modules/auth/components/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      cursor: {
        para: "url(/cursor.svg), pointer",
      },
      fontFamily: {
        sans: ["var(--font-dm-sans)", "DM Sans", "sans-serif"],
        swell: ["var(--font-swell)", "sans-serif"],
      },
      animation: {
        scroll:
          "scroll var(--animation-duration, 40s) var(--animation-direction, forwards) linear infinite",
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
      keyframes: {
        scroll: {
          to: {
            transform: "translate(calc(-50% - 0.5rem))",
          },
        },
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
      },
      colors: {
        background: "var(--background)",
        foreground: "var(--foreground)",
        primary: "#003463",
        secondary: "#FB4005",
        blue: {
          DEFAULT: "#00A7E1",
          dead: "#7C99A9",
          dark: "#003463",
        },
        gray: {
          DEFAULT: "#747474",
          light: "#E5E5E5",
          dark: "#464646",
        },
        orange: {
          DEFAULT: "#FB4005",
          light: "#FEF0C8",
        },
        danger: "#FF0000",
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      screens: {
        "h-M": {
          raw: "(min-height: 450px)",
        },
        "h-L": {
          raw: "(min-height: 600px)",
        },
        "h-regularL": {
          raw: "(min-height: 740px)",
        },
        "h-extraL": {
          raw: "(min-height: 850px)",
        },
        "h-3L": {
          raw: "(min-height: 950px)",
        },
        "h-XL": {
          raw: "(min-height: 1200px)",
        },
        S: "350px",
        M: "450px",
        L: "600px",
        extraTinyL: "640",
        tinyL: "700px",
        regularL: "740px",
        extraL: "850px",
        "2L": "881px",
        "2extraL": "900px",
        "3L": "950px",
        XL: "1200px",
        extraXL: "1330px",
        "2XL": "1600px",
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    require("@tailwindcss/typography"),
    require("tailwind-scrollbar"),
    addVariablesForColors,
  ],
} satisfies Config;

function addVariablesForColors({ addBase, theme }: any) {
  let allColors = flattenColorPalette(theme("colors"));
  let newVars = Object.fromEntries(
    Object.entries(allColors).map(([key, val]) => [`--${key}`, val])
  );

  addBase({
    ":root": newVars,
  });
}
