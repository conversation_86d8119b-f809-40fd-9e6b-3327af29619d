import { useEffect } from "react";

export default function useMenu() {
  useEffect(() => {
    const closeButtons = document.querySelectorAll(".mobile-menu-close");
    const overlay = document.querySelector(".menu-overlay");

    const closeMenu = () => {
      const menu = document.querySelector(".mobile-menu");
      if (menu && overlay) {
        menu.classList.remove("active");
        overlay.classList.remove("active");
        document.body.style.overflow = "";
      }
    };

    const handleResize = () => {
      if (window.innerWidth > 768) {
        closeMenu();
      }
    };

    // Add click listeners to close buttons
    closeButtons.forEach((button) => {
      button.addEventListener("click", closeMenu);
    });

    // Add click listener to overlay
    if (overlay) {
      overlay.addEventListener("click", closeMenu);
    }

    // Add resize listener
    window.addEventListener("resize", handleResize);

    return () => {
      closeButtons.forEach((button) => {
        button.removeEventListener("click", closeMenu);
      });
      if (overlay) {
        overlay.removeEventListener("click", closeMenu);
      }
      window.removeEventListener("resize", handleResize);
      document.body.style.overflow = "";
    };
  }, []);

  return {};
}
