"use client";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";
import { screenWrapperStyle } from "@/components/screen-wrapper";
import { cn } from "@/lib/utils";
import { Separator } from "./ui/separator";
import Facebook from "@assets/icons/platforms/social-media/facebook";
import Instagram from "@assets/icons/platforms/social-media/instagram";
import Email from "@assets/icons/email";
import ContactIcon from "@assets/icons/contact";
import useBrands from "@/modules/catalog/hooks/brands/use-brands";
import { getBrandPageUrl } from "@/modules/catalog/utils/urls";
import { companyEmail, companyPhoneNumber } from "@/data";

export default function Footer() {
  const t = useTranslations("shared.footer");
  const { brands, brandsAreLoading } = useBrands({
    limit: 10,
  });
  return (
    <div className="w-full flex flex-col items-center">
      {/* Main footer content */}
      <div className="w-full flex justify-center bg-primary ">
        <footer
          className={cn(
            screenWrapperStyle,
            "w-full py-12 flex flex-col space-y-8"
          )}
        >
          {/* Main footer grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* First Half: Company Info and Contact */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Company Info */}
              <div className="flex flex-col space-y-4">
                <Image
                  alt="AKAL logo"
                  width={120}
                  height={48}
                  src="/logos/company-white-logo.svg"
                  className="mb-4"
                />

                <div className="space-y-2">
                  <Text
                    textStyle="TS7"
                    className=" whitespace-pre-line text-white"
                  >
                    {t.rich("address", {
                      phone: () => (
                        <Link
                          href={`tel:${companyPhoneNumber}`}
                          className="text-white"
                        >
                          {companyPhoneNumber}
                        </Link>
                      ),
                    })}
                  </Text>
                  <p>
                    <Link href="#" className="text-white underline">
                      <Text textStyle="TS7">{t("mapLink")}</Text>
                    </Link>
                  </p>
                </div>

                {/* Social Media Icons */}
                <div className="flex space-x-3 mt-4">
                  <Link
                    href="https://www.facebook.com/akaltunisia"
                    target="_blank"
                    className="w-8 h-8 flex justify-center items-center rounded"
                  >
                    <Facebook color="#fff" />
                  </Link>
                  <Link
                    href="https://www.facebook.com/akaltunisia"
                    target="_blank"
                    className="w-8 h-8 flex justify-center items-center rounded"
                  >
                    <Instagram color="#fff" />
                  </Link>
                </div>
              </div>

              {/* Contact */}
              <div className="flex flex-col space-y-4">
                <Text textStyle="TS5" className="font-bold text-white">
                  {t("contact.title")}
                </Text>
                <div className="space-y-4">
                  <div className="flex items-start space-x-3">
                    <ContactIcon />
                    <div className="flex flex-col">
                      <Link
                        href={`tel:${companyPhoneNumber}`}
                        className="text-white font-bold"
                      >
                        <Text textStyle="TS5">{companyPhoneNumber}</Text>
                      </Link>
                      <Text
                        textStyle="TS7"
                        className="text-white mt-1 whitespace-pre-line"
                      >
                        {t("contact.workTime")}
                      </Text>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Email />
                    <Link
                      href={`mailto:${companyEmail}`}
                      className="text-white font-bold"
                    >
                      <Text textStyle="TS6">{companyEmail}</Text>
                    </Link>
                  </div>
                </div>
              </div>
            </div>

            {/* Second Half: Useful Links, Account, and Brands (squeezed together) */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
              {/* Useful Links */}
              <div className="flex flex-col space-y-3">
                <Text textStyle="TS5" className="font-bold text-white">
                  {t("usefulLinks.title")}
                </Text>
                <ul className="space-y-2">
                  {t
                    .raw("usefulLinks.links")
                    .map(
                      (
                        { content, link }: { content: string; link: string },
                        idx: number
                      ) => (
                        <li key={idx}>
                          <Link
                            href={link}
                            className="text-white hover:underline transition-colors"
                          >
                            <Text textStyle="TS7">{content}</Text>
                          </Link>
                        </li>
                      )
                    )}
                </ul>
              </div>

              {/* Account */}
              <div className="flex flex-col space-y-3">
                <Text textStyle="TS5" className="font-bold text-white">
                  {t("account.title")}
                </Text>
                <ul className="space-y-2">
                  {t
                    .raw("account.links")
                    .map(
                      (
                        { content, link }: { content: string; link: string },
                        idx: number
                      ) => (
                        <li key={idx}>
                          <Link
                            href={link}
                            className="text-white hover:underline transition-colors"
                          >
                            <Text textStyle="TS7">{content}</Text>
                          </Link>
                        </li>
                      )
                    )}
                </ul>
              </div>

              {/* Brands */}
              <div className="flex flex-col space-y-3">
                <Text textStyle="TS5" className="font-bold text-white">
                  {t("brands.title")}
                </Text>
                <ul className="space-y-2">
                  {!brandsAreLoading && brands
                    ? brands
                        .filter((brand) => brand.numberOfProducts > 0)
                        .slice(0, 5)
                        .map((brand) => (
                          <li key={brand.id}>
                            <Link
                              href={getBrandPageUrl(brand.slug)}
                              className="text-white hover:underline transition-colors"
                            >
                              <Text textStyle="TS7">{brand.name}</Text>
                            </Link>
                          </li>
                        ))
                    : Array.from({ length: 3 }).map((_, idx) => (
                        <li key={idx}>
                          <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                        </li>
                      ))}
                </ul>
              </div>
            </div>
          </div>

          <Separator className="my-8 border-gray-200" />

          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <Text textStyle="TS7" className="text-white">
              {t.rich("copyRight", {
                link: (chunk) => (
                  <a target="_blank" href="https://tdg.tn">
                    {chunk}
                  </a>
                ),
              })}
            </Text>
          </div>
        </footer>
      </div>
    </div>
  );
}
