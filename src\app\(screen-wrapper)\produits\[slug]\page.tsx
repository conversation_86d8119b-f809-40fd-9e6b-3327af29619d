import ProductInfoPage from "@/modules/catalog/components/products/product/page";
import { retrieveSimilarProductsFromServerSide } from "@/modules/catalog/services/products/similar-products-extraction";
import { generateProductMetadata } from "@/modules/catalog/utils/seo/meta-data/products";
import { getProductPageUrl } from "@/modules/catalog/utils/urls";
import { Metadata } from "next";

interface Props {
  params: Promise<{ slug: string }>;
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { slug } = await params;
  const productMetadata = await generateProductMetadata({ productSlug: slug });
  productMetadata.metadataBase = new URL(
    `https://${process.env.FRONTEND_DOMAIN_NAME as string}`
  );
  productMetadata.alternates = {
    canonical: getProductPageUrl(slug),
  };

  return productMetadata;
}

export default async function ProductDetail({ params }: Props) {
  const { slug } = await params;

  const productSimilarProductsData =
    await retrieveSimilarProductsFromServerSide({
      page: 1,
      limit: 8,
      productSlug: slug,
    });

  const categoryProducts = productSimilarProductsData
    ? productSimilarProductsData.products
    : [];

  return (
    <>
      <ProductInfoPage slug={slug} />

      {/* making products crawlable for seo purposes */}
      <div className="max-w-0 max-h-0 overflow-hidden">
        {categoryProducts.map((product, idx) => (
          <a key={idx} href={getProductPageUrl(product.slug)} className="">
            {product.name}
          </a>
        ))}
      </div>
    </>
  );
}
