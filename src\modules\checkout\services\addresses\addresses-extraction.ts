import { GET } from "@/lib/http-methods";
import { AxiosError } from "axios";
import {
  AddressResponseDataType,
  AddressType,
} from "@/modules/checkout/types/addresses";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import { castToAddressType } from "../../utils/types-casting/addresses";

export async function retrieveUserAddresses(): Promise<AddressType[] | null> {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = await GET(`${process.env.BACKEND_ADDRESS}/addresses`, header);

    return (res.data as AddressResponseDataType[]).map((address) =>
      castToAddressType(address)
    );
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(retrieveUserAddresses);

      //unauthorized user error is already handled by the user hook
      if (!res) return [];
      return res;
    }

    return [];
  }
}
