import { useQuery } from "@tanstack/react-query";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import { retrieveUserOrders } from "../services/orders/orders-extraction";

export default function useUserOrders(limit: number) {
  const pathname = usePathname();
  const [page, setPage] = useState(1);
  const [pagesNumber, setPagesNumber] = useState(1);
  const { data, isLoading, isError } = useQuery({
    queryKey: ["user-orders", page],
    queryFn: () => retrieveUserOrders(page, limit),
    enabled: !["my-account", "my-account/info", "my-account/settings"].every(
      (disabledPathname) => pathname.endsWith(disabledPathname)
    ), //data in those pages should not be fetched
  });

  //updating pages number once data is fetched
  useEffect(() => {
    if (data)
      setPagesNumber(
        data?.pagination?.totalPages ? data?.pagination?.totalPages : 1
      );
  }, [data]);

  return {
    orders: data ? data.orders : null,
    ordersAreLoading: isLoading,
    pagesNumber,
    currentPage: page,
    setPage,
  };
}
