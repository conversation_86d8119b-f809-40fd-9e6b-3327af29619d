import { Country } from "@shopify/address";
import parsePhoneNumberFromString from "libphonenumber-js";
import { City } from "../types/addresses";

export default function verifyAddressContent(
  formData: FormData,
  optionnalLabels: string[],
  country: Country,
  city: City,
  warningsContent: (key: string) => string
) {
  const wrongInputsFinded: string[] = [];
  let warning = "";
  const neededData = [
    "email",
    "firstName",
    "lastName",
    "address1",
    "address2",
    "company",
    "province",
    "zip",
    "phone",
  ];
  const addressData: {
    [key: string]: string;
  } = {};

  neededData.forEach((dataId) => {
    //in case data doesn't exist we pass it
    if (formData.get(dataId) === null) return;

    if (formData.get(dataId)?.toString().trim() !== "") {
      //zip is the postal code in server side schema
      if (dataId === "zip")
        addressData["postalCode"] = formData.get(dataId) as string;
      else if (dataId === "phone")
        addressData["phone"] = `+${country?.phoneNumberPrefix}${formData.get(
          "phone"
        )}`;
      else addressData[dataId] = formData.get(dataId) as string;
    } else if (!optionnalLabels.includes(dataId)) {
      wrongInputsFinded.push(dataId);
    }
  });

  //city addition
  if (country?.code !== "TN") {
    //in case city is an input (countries other then libya)
    if (formData.get("city")?.toString().trim() !== "")
      addressData["cityId"] = formData.get("city") as string;
    else wrongInputsFinded.push("city");
  } //we get the city from the dropdown menu
  else addressData["cityId"] = city.code;

  //user need to complete inputs fields before continue verification
  if (wrongInputsFinded.length > 0)
    return {
      addressData: addressData,
      valid: wrongInputsFinded.length === 0,
      wrongInputsFinded,
      warning: warningsContent("addressCreation.fillAllFields"),
    };

  //phone number verification
  const phoneNumber = `+${country?.phoneNumberPrefix}${formData.get("phone")}`;

  if (!parsePhoneNumberFromString(phoneNumber)?.isValid()) {
    wrongInputsFinded.push("phone");
    warning = warningsContent("addressCreation.enterValidPhoneNumber");
  }

  return {
    addressData: addressData,
    valid: wrongInputsFinded.length === 0,
    wrongInputsFinded,
    warning,
  };
}
