"use client";
import { screenWrapperStyle } from "@/components/screen-wrapper";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import Image from "next/image";
import Link from "next/link";

export default function Error() {
  const t = useTranslations("500");

  return (
    <div className="w-full flex justify-center">
      <div className="relative">
        <Image
          src="/background-effects/organic-petal-form.svg"
          alt="organic petal form"
          width={374}
          height={320}
          className="absolute -left-10 -top-10 w-[374px] h-[320px]"
        />
        <div className={cn("", screenWrapperStyle)}>
          <div className="relative z-30 w-full flex L:flex-row flex-col L:justify-between items-center L:space-y-0 space-y-4">
            <div className="flex flex-col L:items-start items-center space-y-4">
              <span className="text-primary text-[120px]">500</span>
              <Text
                textStyle="TS2"
                className="font-bold L:text-start text-center"
              >
                {t("title")}
              </Text>
              <Text
                textStyle="TS5"
                className="font-bold L:text-start text-center"
              >
                {t.rich("description", {
                  link: (chunk) => (
                    <Link
                      href={"/filters"}
                      className="text-primary relative before:content-[''] before:absolute before:left-0 before:bottom-[-4px] before:w-full before:h-[2px] before:bg-primary"
                    >
                      {chunk}
                    </Link>
                  ),
                })}
              </Text>
              <Button className="w-[180px] bg-secondary text-white border-secondary hover:bg-white hover:text-secondary hover:border-secondary">
                <Text textStyle="TS7" className="">
                  {t("visitMarket")}
                </Text>
              </Button>
            </div>
            <Image
              src="/404/tears.svg"
              alt="organic petal form"
              width={679}
              height={621}
              className="XL:w-fit 2extraL:w-[500px] regularL:w-[400px] w-full max-w-[400px]"
            />
          </div>
        </div>
      </div>
    </div>
  );
}
