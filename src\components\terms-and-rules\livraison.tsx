import BreadCrumb from "@/components/breadcrumb";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import Link from "next/link";

export const Livraison = () => {
  const t = useTranslations("livraison");

  return (
    <div>
      <BreadCrumb />

      <div className="text-3xl font-bold text-primary mt-12 mb-5">
        <Text textStyle="TS2">{t("title")}</Text>
      </div>

      <div className="px-4 L:px-8 py-5 space-y-8">
        <Text textStyle="TS5">
          {t.rich("paragraph1", {
            a: (chunks) => (
              <Link href="/" className="text-primary underline inline">
                {chunks}
              </Link>
            ),
            br: () => <br />,
          })}
        </Text>

        <div>
          <div className="text-xl font-semibold mt-6">
            <Text textStyle="TS4">{t("section1.title")}</Text>
          </div>
          <ul className="list-disc list-inside">
            <li>
              <Text textStyle="TS5" className="font-semibold inline">
                {t("section1.item1")}
              </Text>
            </li>
            <li>
              <Text textStyle="TS5" className="font-semibold inline">
                {t("section1.item2")}
              </Text>
            </li>
          </ul>
        </div>

        <div>
          <div className="text-xl font-semibold mt-6">
            <Text textStyle="TS4">{t("section2.title")}</Text>
          </div>
          <Text textStyle="TS5">
            {t.rich("section2.paragraph1", {
              a: (chunks) => (
                <Link href="/" className="text-primary underline inline">
                  {chunks}
                </Link>
              ),
              br: () => <br />,
            })}
          </Text>
        </div>

        <div>
          <div className="text-xl font-semibold mt-6">
            <Text textStyle="TS4">{t("section3.title")}</Text>
          </div>
          <Text textStyle="TS5">
            {t.rich("section3.paragraph1", {
              br: () => <br />,
            })}
          </Text>
        </div>
      </div>
    </div>
  );
};
