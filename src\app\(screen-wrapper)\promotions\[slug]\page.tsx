import { cn } from "@/lib/utils";
import PromotionProductsLists from "@/modules/catalog/components/products/promotion-products-list";
import retrievePromotionsFromServerSide from "@/modules/catalog/services/promotions/promotions-extraction";
import Text from "@/styles/text-styles";
import { notFound } from "next/navigation";

interface Props {
  params: Promise<{ slug: string }>;
}

export default async function PromotionPage({ params }: Props) {
  const { slug } = await params;
  const promotions = await retrievePromotionsFromServerSide({});
  const promotion = promotions.find(
    (searchedPromotion) => searchedPromotion.slug === slug
  );

  if (!promotion) notFound();

  return (
    <div className={cn("w-full pt-5 flex flex-col gap-8")}>
      <h3 className="w-full flex justify-center text-primary font-bold">
        <Text textStyle="TS1.1">{promotion.name}</Text>
      </h3>
      <PromotionProductsLists promotionSlug={slug} />
    </div>
  );
}
