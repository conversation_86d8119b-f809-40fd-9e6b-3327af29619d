"use client";

import * as React from "react";
import { Drawer as DrawerPrimitive } from "vaul";

import { cn } from "@/lib/utils";

const TopDrawer = ({
  shouldScaleBackground = true,
  ...props
}: React.ComponentProps<typeof DrawerPrimitive.Root> & {
  shouldScaleBackground?: boolean;
}) => (
  <DrawerPrimitive.Root
    shouldScaleBackground={shouldScaleBackground}
    {...props}
  />
);
TopDrawer.displayName = "TopDrawer";

const TopDrawerTrigger = DrawerPrimitive.Trigger;

const TopDrawerPortal = DrawerPrimitive.Portal;

const TopDrawerClose = DrawerPrimitive.Close;

const TopDrawerOverlay = React.forwardRef<
  React.ElementRef<typeof DrawerPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Overlay>
>(({ className, ...props }, ref) => (
  <DrawerPrimitive.Overlay
    ref={ref}
    className={cn(
      "fixed inset-0 z-50 bg-black/80 data-[state=closed]:animate-none data-[state=open]:animate-none transition-none",
      className
    )}
    {...props}
  />
));
TopDrawerOverlay.displayName = "TopDrawerOverlay";

const TopDrawerContent = React.forwardRef<
  React.ElementRef<typeof DrawerPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Content>
>(({ className, children, ...props }, ref) => {
  const contentRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const handleViewportChange = () => {
      if (contentRef.current && window.innerWidth < 768) {
        const currentHeight = contentRef.current.style.height;
        if (currentHeight && currentHeight !== 'auto') {
          contentRef.current.style.minHeight = currentHeight;
        }
      }
    };

    if (typeof window !== 'undefined' && window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleViewportChange);
      return () => window.visualViewport?.removeEventListener('resize', handleViewportChange);
    }
  }, []);

  return (
    <TopDrawerPortal>
      <TopDrawerOverlay />
      <DrawerPrimitive.Content
        ref={(node) => {
          contentRef.current = node;
          if (typeof ref === 'function') {
            ref(node);
          } else if (ref) {
            ref.current = node;
          }
        }}
        className={cn(
          "fixed inset-x-0 top-0 z-50 mb-24 flex flex-col rounded-b-[10px] border border-neutral-200 bg-white dark:border-neutral-800 dark:bg-neutral-950 data-[state=closed]:animate-none data-[state=open]:animate-none transition-none",
          className
        )}
        {...props}
      >
        <div className="mx-auto mb-4 mt-auto h-2 w-[100px] rounded-full bg-neutral-100 dark:bg-neutral-800" />
        {children}
      </DrawerPrimitive.Content>
    </TopDrawerPortal>
  );
});
TopDrawerContent.displayName = "TopDrawerContent";

const TopDrawerHeader = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn("grid gap-1.5 p-4 text-center sm:text-left", className)}
    {...props}
  />
);
TopDrawerHeader.displayName = "TopDrawerHeader";

const TopDrawerFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn("mt-auto flex flex-col gap-2 p-4", className)}
    {...props}
  />
);
TopDrawerFooter.displayName = "TopDrawerFooter";

const TopDrawerTitle = React.forwardRef<
  React.ElementRef<typeof DrawerPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Title>
>(({ className, ...props }, ref) => (
  <DrawerPrimitive.Title
    ref={ref}
    className={cn(
      "text-lg font-semibold leading-none tracking-tight",
      className
    )}
    {...props}
  />
));
TopDrawerTitle.displayName = "TopDrawerTitle";

const TopDrawerDescription = React.forwardRef<
  React.ElementRef<typeof DrawerPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Description>
>(({ className, ...props }, ref) => (
  <DrawerPrimitive.Description
    ref={ref}
    className={cn("text-sm text-neutral-500 dark:text-neutral-400", className)}
    {...props}
  />
));
TopDrawerDescription.displayName = "TopDrawerDescription";

export {
  TopDrawer,
  TopDrawerPortal,
  TopDrawerOverlay,
  TopDrawerTrigger,
  TopDrawerClose,
  TopDrawerContent,
  TopDrawerHeader,
  TopDrawerFooter,
  TopDrawerTitle,
  TopDrawerDescription,
};
