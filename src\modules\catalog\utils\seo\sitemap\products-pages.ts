import { retrieveProductsFromServerSide } from "@/modules/catalog/services/products/products-extraction";
import { MetadataRoute } from "next";
import { getProductPageUrl, getSimilarProductsPage } from "../../urls";

export async function getProductsPages(): Promise<MetadataRoute.Sitemap> {
  try {
    const data = await retrieveProductsFromServerSide({ page: 1, limit: 500 });

    if (data) {
      const pagination = data.pagination;
      const productsPages: MetadataRoute.Sitemap = data.products.flatMap(
        (product) => [
          {
            url: `https://${
              process.env.FRONTEND_DOMAIN_NAME
            }${getProductPageUrl(product.slug)}`,
            lastModified: new Date(),
            changeFrequency: "monthly",
            priority: 1,
          },
          {
            url: `https://${
              process.env.FRONTEND_DOMAIN_NAME
            }${getSimilarProductsPage(product.slug)}`,
            lastModified: new Date(),
            changeFrequency: "monthly",
            priority: 1,
          },
        ]
      );

      for (let page = 2; page <= pagination.totalPages; page++) {
        const data = await retrieveProductsFromServerSide({ page, limit: 500 });
        if (data) {
          const currentPageProductsPages: MetadataRoute.Sitemap =
            data.products.flatMap((product) => [
              {
                url: `https://${
                  process.env.FRONTEND_DOMAIN_NAME
                }${getProductPageUrl(product.slug)}`,
                lastModified: new Date(),
                changeFrequency: "monthly",
                priority: 1,
              },
              {
                url: `https://${
                  process.env.FRONTEND_DOMAIN_NAME
                }${getSimilarProductsPage(product.slug)}`,
                lastModified: new Date(),
                changeFrequency: "monthly",
                priority: 1,
              },
            ]);
          productsPages.push(...currentPageProductsPages);
        }
      }

      return productsPages;
    }
  } catch {
    return [];
  }

  return [];
}
