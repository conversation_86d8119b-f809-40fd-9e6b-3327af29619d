import { NextRequest, NextResponse } from "next/server";
import privateAccessMiddleware from "./modules/auth/middlewares/private-access-middlware";
import { setupResponseCookies } from "./modules/auth/middlewares/middleware-cookies";
import externalAuthMiddlware from "./modules/auth/middlewares/external-auth-middlware";

export async function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl;
  let urlUpdated = false;

  // verification of the private access of the user
  const verifiedUserAccess = await privateAccessMiddleware(req);

  //this variable is used to handle the private access issue it will be deleted in public production env.
  let externalAuthVerified = false;

  if (
    pathname.includes("/google-auth") &&
    req.nextUrl.searchParams.get("code")
  ) {
    externalAuthVerified = await externalAuthMiddlware(req, "google");

    urlUpdated = true;
  }

  // check if lang is updated or to redirect user  if needed
  // if (!verifiedUserAccess && !externalAuthVerified)
  //   return NextResponse.redirect("https://test.tawer.tn/en-US/not-found");
  // else if (pathname !== req.nextUrl.pathname) urlUpdated = true;

  if (urlUpdated) {
    if (pathname !== req.nextUrl.pathname) {
      //only pathname is update
      const res = NextResponse.redirect(req.nextUrl);

      setupResponseCookies(req, res);

      return res;
    } //only url params are updated
    else return NextResponse.rewrite(req.nextUrl);
  }
  return NextResponse.next();
}

//excluding the apis and _next folder from the middleware
export const config = {
  matcher: [
    // Skip all internal paths (_next) and image files
    "/((?!api|_next|.*\\.(?:png|jpg|jpeg|gif|svg|webp|ico|avif)$).*)",
  ],
};
