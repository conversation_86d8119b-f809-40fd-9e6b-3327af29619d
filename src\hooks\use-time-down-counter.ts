import { Dayjs } from "dayjs";
import { useEffect, useRef, useState } from "react";

export default function useTimeDownCounter(targetDate: Dayjs | undefined) {
  const [timeDownCounter, setTimeDownCounter] = useState(0);
  const counterId = useRef(-1);

  useEffect(() => {
    if (targetDate) {
      if (counterId.current !== -1) cancelAnimationFrame(counterId.current);

      function startCounting() {
        setTimeout(() => {
          const currentDate = new Date();
          const datesDifference = targetDate
            ? targetDate.toDate().getTime() - currentDate.getTime()
            : 0;
          if (datesDifference > 0) setTimeDownCounter(datesDifference);
          else setTimeDownCounter(0);

          counterId.current = requestAnimationFrame(startCounting);
        }, 1000);
      }

      counterId.current = requestAnimationFrame(startCounting);
    }

    return () => {
      cancelAnimationFrame(counterId.current);
    };
  }, [targetDate]);

  const seconds = Math.floor((timeDownCounter / 1000) % 60);
  const minutes = Math.floor((timeDownCounter / 60000) % 60);
  const hours = Math.floor(timeDownCounter / 3600000);

  return {
    Days: Math.floor(timeDownCounter / 3600000 / 24),
    Hours: String(hours).padStart(2, "0"),
    Minutes: String(minutes).padStart(2, "0"),
    Seconds: String(seconds).padStart(2, "0"),
  };
}
