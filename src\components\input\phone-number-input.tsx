import { cn } from "@/lib/utils";
import Text, { TextStyle } from "@/styles/text-styles";
import { HTMLAttributes } from "react";

interface PhoneNumberPropsType extends HTMLAttributes<HTMLDivElement> {
  code: string;
  inputClassName?: string;
  inputName: string;
  primaryTheme?: boolean;
}

export default function PhoneNumberInput({
  code,
  className,
  inputName,
  inputClassName,
  primaryTheme = true,
}: PhoneNumberPropsType) {
  return (
    <div
      className={cn(
        "h-10 pl-3 flex items-center bg-opacity-55 border border-gray rounded-[10px]",
        className,
        {
          "border-primary": primaryTheme,
        }
      )}
      dir="ltr"
    >
      <Text
        textStyle="TS5"
        className={cn("border-gray pr-3 border-r", inputClassName, {
          "border-primary": primaryTheme,
        })}
      >
        {code}
      </Text>
      <input
        name={inputName}
        className={cn(
          "px-2 flex-1 h-full bg-transparent outline-none",
          TextStyle["TS5"]
        )}
      />
    </div>
  );
}
