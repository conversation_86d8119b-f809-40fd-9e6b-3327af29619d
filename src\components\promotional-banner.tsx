import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";

export default function PromotionalBanner() {
  const t = useTranslations("shared.promotionalBanner");
  return (
    <div className="w-full bg-gray/10 py-3">
      <div className="max-w-[1500px] mx-auto px-4 regularL:px-32">
        <div className="flex flex-col md:flex-row items-center justify-between gap-4 md:gap-8 text-white ">
          <div className="flex items-center gap-2">
            <Text textStyle="TS7" className="text-primary font-bold">
              {t("satisfactionGuarantee")}
            </Text>
          </div>

          <div className="hidden md:block w-px h-7 bg-gray"></div>

          <div className="flex items-center gap-2">
            <Text textStyle="TS7" className="text-primary font-bold">
              {t("fastDelivery")}
            </Text>
          </div>

          <div className="hidden md:block w-px h-7 bg-gray"></div>

          <div className="flex items-center gap-2">
            <Text textStyle="TS7" className="text-primary font-bold">
              {t.rich("freeShipping", {
                amount: (chunks) => <strong>{chunks}</strong>,
              })}
            </Text>
          </div>
        </div>
      </div>
    </div>
  );
}
