import { getBrandsPages } from "@/modules/catalog/utils/seo/sitemap/brands-pages";
import { getCategoriesPages } from "@/modules/catalog/utils/seo/sitemap/categories-pages";
import { getProductsPages } from "@/modules/catalog/utils/seo/sitemap/products-pages";
import { getPromotionsPages } from "@/modules/catalog/utils/seo/sitemap/promotions-pages";
import { StaticPagesUrls } from "@/utils/sitemap/static-pages";
import type { MetadataRoute } from "next";

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const productsPages = await getProductsPages();
  const brandsPages = await getBrandsPages();
  const categoriesPages = await getCategoriesPages();
  const promotionsPages = await getPromotionsPages();

  return [
    ...StaticPagesUrls,
    ...productsPages,
    ...brandsPages,
    ...categoriesPages,
    ...promotionsPages,
  ];
}
