import { cn } from "@/lib/utils";
import { HTMLAttributes } from "react";

const styles = {
  HS: "sm:text-[48px] lg:text-[56px] text-[32px]",
  TS1: "XL:text-[69px] L:text-[48px] M:text-[40px] text-[32px]",
  TS2: "XL:text-[36px] L:text-[32px] M:text-[24px] text-[20px]",
  TS3: "XL:text-[32px] L:text-[24px] M:text-[18px] text-[16px]",
  TS4: "XL:text-[24px] L:text-[20px] M:text-[15px] text-[13px]",
  TS5: "XL:text-[20px] L:text-[16px] text-[15px]",
  TS6: "XL:text-[16px] text-[14px]",
  TS7: "XL:text-[14px] text-[13px]",
  TS8: "XL:text-[12px] M:text-[10px] text-[9px]",
  TS9: "XL:text-[10px] L:text-[9px] M:text-[8px] text-[7px]",
  "TS1.1": "XL:text-[50px] L:text-[46px] M:text-[38px] text-[32px]",
};

export type TextStyleType =
  | "HS"
  | "TS1"
  | "TS2"
  | "TS3"
  | "TS4"
  | "TS5"
  | "TS6"
  | "TS7"
  | "TS8"
  | "TS9"
  | "TS1.1";

export interface TextStylePropsType extends HTMLAttributes<HTMLSpanElement> {
  textStyle: TextStyleType;
}

export default function Text({
  textStyle,
  children,
  className,
}: TextStylePropsType) {
  return <span className={cn(className, styles[textStyle])}>{children}</span>;
}

export const TextStyle = styles;
