import Image from "next/image";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { screenWrapperStyle } from "../screen-wrapper";
import useUser from "@/modules/auth/hooks/use-user";
import useExternalAuth from "@/modules/auth/hooks/use-external-auth-validation";
import { Button } from "../ui/button";
import { Heart, User } from "lucide-react";
import { useRouter, usePathname } from "next/navigation";
import useScreenSize from "@/hooks/use-screen-size";
import SearchBar from "@/components/search-bar";
import { useWishlist } from "@/modules/wishlist/store/wishlist-store";
import Cart from "@/modules/cart/components/cart";
import Text from "@/styles/text-styles";
import UserIcon from "@assets/icons/user/user";

export default function AppHeader() {
  const { user } = useUser();
  const { wishlistItems } = useWishlist();
  const router = useRouter();
  const pathname = usePathname();
  const { width } = useScreenSize();
  const isMobile = width < 768; // md

  useExternalAuth();

  return (
    <div
      className={cn(
        "relative w-full max-h-16 py-2 z-50 flex flex-col items-center justify-center border",
        {
          "h-auto": isMobile,
          "h-24": !isMobile,
        }
      )}
    >
      <div
        className={cn(
          "flex-1 w-full flex items-center justify-between",
          screenWrapperStyle,
          {
            "flex-col items-center": isMobile,
            "flex-row": !isMobile,
          }
        )}
      >
        {isMobile ? (
          <div className="py-2 flex justify-between items-center w-full ">
            <div className="w-full L:w-fit justify-start">
              <Link href={"/"} className="">
                <Image
                  width={104}
                  height={42}
                  alt="Akal logo"
                  src={"/logos/akal-logo.svg"}
                  priority
                  className="h-9 aspect-[42/104]"
                />
              </Link>
            </div>
            <div>
              <SearchBar />
            </div>
          </div>
        ) : (
          <>
            <div className="max-w-[900px] flex items-center space-x-8 w-full justify-between">
              <Link href={"/"} className="">
                <Image
                  width={104}
                  height={42}
                  alt="Akal logo"
                  src={"/logos/akal-logo.svg"}
                  priority
                  className="w-[104px] aspect-[104/42]"
                />
              </Link>
              <SearchBar />
            </div>
          </>
        )}
        {!isMobile && (
          <div className="flex items-center space-x-4 ms-8 ">
            {/* User Icon */}
            <div className="relative">
              {user && user.isAuthenticated ? (
                <Button
                  variant="ghost"
                  size="icon"
                  className={`z-10 rounded-full ${
                    pathname.startsWith("/mon-compte")
                      ? "bg-blue text-white"
                      : "bg-gray/10 text-blue"
                  }`}
                  onClick={() => router.push("/mon-compte")}
                >
                  <User size={20} />
                </Button>
              ) : (
                <Link href="/auth/sign-in">
                  <div
                    className={`rounded-full w-9 h-9 flex justify-center items-center ${
                      pathname.startsWith("/auth")
                        ? "bg-blue text-white"
                        : "bg-gray/10 text-blue"
                    }`}
                  >
                    <UserIcon
                      color={
                        pathname.startsWith("/auth") ? "white" : "currentColor"
                      }
                    />
                  </div>
                </Link>
              )}
            </div>

            {/* Heart Icon */}
            <div className="relative">
              {wishlistItems.length > 0 && (
                <Text
                  textStyle="TS8"
                  className="absolute -right-2 -top-[6px] w-[20px] h-[20px] p-[2px] bg-blue rounded-full flex items-center justify-center text-white z-10"
                >
                  {wishlistItems.length}
                </Text>
              )}

              <Button
                variant="ghost"
                className={cn(
                  "rounded-full shadow-sm transition-all duration-200 flex items-center justify-center w-8 h-8 S:w-9 S:h-9 p-1.5 S:p-2 aspect-square",
                  pathname.startsWith("/produits/favories")
                    ? "bg-blue"
                    : "bg-gray/10"
                )}
                onClick={() => router.push("/produits/favories")}
              >
                <Heart
                  className={cn(
                    "transition-colors duration-200 w-3 h-4 S:w-5 S:h-5",
                    pathname.startsWith("/produits/favories")
                      ? "text-white"
                      : "text-blue"
                  )}
                  strokeWidth={1.5}
                />
              </Button>
            </div>

            <Cart />
          </div>
        )}

        {isMobile && <Cart />}
      </div>
    </div>
  );
}
