import { GET } from "@/lib/http-methods";
import { castToPointsRate } from "../utils/types-casting/points-rate";
import { PointsRateType, PointsRateTypeInResponse } from "../types";
import { CustomError } from "@/utils/custom-error";

export async function retrievePointsRateFromServerSide(): Promise<PointsRateType> {
  try {
    const res = await GET(`/points-rate`, {});

    return castToPointsRate(res.data as PointsRateTypeInResponse);
  } catch (error) {
    throw new CustomError("Server Error!", 500);
  }
}
