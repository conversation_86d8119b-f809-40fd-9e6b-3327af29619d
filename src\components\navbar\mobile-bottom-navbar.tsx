"use client";
import { Heart, Home, ShoppingBag, User } from "lucide-react";
import { Button } from "../ui/button";
import { useRouter, usePathname } from "next/navigation";
import { useCartStore } from "@/modules/cart/store/cart-store";
import useUser from "@/modules/auth/hooks/use-user";
import useScreenSize from "@/hooks/use-screen-size";
import HamburgerMenuButton from "./hamburger-menu-button";
import { useTranslations } from "next-intl";
import UserIcon from "@assets/icons/user/user";
import Text from "@/styles/text-styles";
import Link from "next/link";
import useCartVisibility from "@/modules/cart/store/cart-visibility-store";
import { useWishlist } from "@/modules/wishlist/store/wishlist-store";

export default function MobileBottomNavbar() {
  const t = useTranslations("shared.navbar.mobileMenu");
  const router = useRouter();
  const pathname = usePathname();
  const { cartItems } = useCartStore((store) => store.state);
  const { user } = useUser();
  const { width } = useScreenSize();
  const { setCartIsOpen, cartIsOpen } = useCartVisibility();
  const isMobile = width < 768;
  const { wishlistItems } = useWishlist();

  if (!isMobile) return null;

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50 bg-white/95 border-t border-gray-200 shadow-lg md:hidden mobile-bottom-navbar max-w-full overflow-hidden">
      <div className="flex items-center justify-around py-2 px-2 w-full max-w-full overflow-hidden">
        {/* Hamburger Menu Button */}
        <div className="flex flex-col items-center p-1 h-auto min-w-0">
          <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
            <HamburgerMenuButton />
          </div>
        </div>

        {/* Home */}
        <Button
          variant="ghost"
          size="icon"
          className="flex flex-col items-center p-1 h-auto min-w-0 "
          onClick={() => router.push("/")}
          aria-label={t("home")}
        >
          <div
            className={`w-8 h-8 rounded-full flex items-center justify-center ${
              pathname === "/" ? "bg-primary" : "bg-gray/10"
            }`}
          >
            <Home
              size={18}
              className={pathname === "/" ? "text-white" : "text-primary"}
            />
          </div>
        </Button>

        <div className="flex flex-col items-center">
          {user && user.isAuthenticated ? (
            <Button
              variant="ghost"
              size="icon"
              className="flex flex-col items-center p-1 h-auto min-w-0"
              onClick={() => router.push("/mon-compte")}
              aria-label={t("account")}
            >
              <div
                className={`rounded-full flex items-center justify-center w-8 h-8 S:w-9 S:h-9 p-1.5 S:p-2 ${
                  pathname.startsWith("/mon-compte")
                    ? "bg-primary"
                    : "bg-gray/10"
                }`}
              >
                <User
                  size={18}
                  className={
                    pathname.startsWith("/mon-compte")
                      ? "text-white "
                      : "text-primary "
                  }
                />
              </div>
            </Button>
          ) : (
            <Link href="/auth/sign-in" className="flex flex-col items-center">
              <div
                className={`rounded-full flex items-center justify-center w-8 h-8 S:w-9 S:h-9 p-1.5 S:p-2 ${
                  pathname.startsWith("/auth")
                    ? "bg-primary text-white"
                    : "bg-gray/10 text-primary"
                }`}
              >
                <UserIcon
                  color={
                    pathname.startsWith("/auth") ? "white" : "currentColor"
                  }
                />
              </div>
            </Link>
          )}
        </div>

        <div className="flex flex-col items-center p-1 h-auto relative min-w-0 ">
          {/* Heart Icon */}
          <div className="relative">
            {wishlistItems.length > 0 && (
              <Text
                textStyle="TS8"
                className="absolute -right-2 -top-[6px] w-[20px] h-[20px] p-[2px] bg-primary rounded-full flex items-center justify-center text-white z-10"
              >
                {wishlistItems.length}
              </Text>
            )}

            <Button
              variant="ghost"
              className={`rounded-full shadow-sm transition-all duration-200 flex items-center justify-center w-8 h-8 S:w-9 S:h-9 p-1.5 S:p-2 aspect-square ${
                pathname.startsWith("/produits/favories")
                  ? "bg-primary"
                  : "bg-gray/10"
              }`}
              onClick={() => router.push("/produits/favories")}
            >
              <Heart
                className={`transition-colors duration-200 w-3 h-4 S:w-5 S:h-5 ${
                  pathname.startsWith("/produits/favories")
                    ? "text-white"
                    : "text-primary"
                }`}
                strokeWidth={1.5}
              />
            </Button>
          </div>
        </div>

        {/* Cart */}
        <Button
          variant="ghost"
          size="icon"
          className="flex flex-col items-center p-1 h-auto relative min-w-0"
          onClick={() => setCartIsOpen(true)}
          aria-label={t("cart")}
        >
          <div
            className={`rounded-full flex items-center justify-center relative overflow-visible w-8 h-8 S:w-9 S:h-9 p-1.5 S:p-2 ${
              cartIsOpen || pathname.startsWith("/paiement")
                ? "bg-primary"
                : "bg-gray/10"
            }`}
          >
            <ShoppingBag
              size={18}
              className={
                cartIsOpen || pathname.startsWith("/paiement")
                  ? "text-white"
                  : "text-primary"
              }
            />
            {cartItems && cartItems.length > 0 && (
              <div className="absolute -top-0.5 -right-0.5 w-4 h-4 bg-primary rounded-full flex items-center justify-center">
                <span className="text-white text-xs font-medium">
                  {cartItems.length}
                </span>
              </div>
            )}
          </div>
        </Button>
      </div>
    </div>
  );
}
