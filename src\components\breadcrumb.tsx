"use client";
import {
  Breadcrumb,
  Bread<PERSON>rumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { useTranslations } from "next-intl";
import React, { useEffect, useState } from "react";

export default function BreadCrumb() {
  const [links, setLinks] = useState<string[]>([]);
  const t = useTranslations("shared.navbar");

  useEffect(() => {
    const path = window.location.pathname;

    const pathSegments = path
      .split("/")
      .filter((segment) => segment !== "" && segment !== "filtres");

    setLinks(pathSegments);
  }, []);
  return (
    <Breadcrumb>
      <BreadcrumbList className="text-primary">
        <BreadcrumbItem>
          <BreadcrumbLink href="/" className="hover:text-primary">
            {t.raw("home")}
          </BreadcrumbLink>
        </BreadcrumbItem>

        {links.map((link, idx) => (
          <React.Fragment key={idx}>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              {idx === links.length - 1 ? (
                <BreadcrumbPage className="text-primary">{link}</BreadcrumbPage>
              ) : (
                <BreadcrumbLink
                  href={`/${link}`}
                  className="hover:text-primary hover:no-underline"
                >
                  {link}
                </BreadcrumbLink>
              )}
            </BreadcrumbItem>
          </React.Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
