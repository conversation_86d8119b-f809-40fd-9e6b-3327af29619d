import BreadCrumb from "@/components/breadcrumb";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import Link from "next/link";

export const Contact = () => {
  const t = useTranslations("contact");

  return (
    <div>
      <BreadCrumb />

      <div className="text-3xl font-bold text-primary mt-4">
        <Text textStyle="TS2">{t("title")}</Text>
      </div>

      <div className="px-4 L:px-8 py-5 space-y-2">
        <div>
          <Text textStyle="TS5">
            {t.rich("paragraph", {
              a: (chunk) => (
                <Link
                  href="https://www.akal.tn"
                  className="text-primary underline inline"
                >
                  {chunk}
                </Link>
              ),
              a1: (chunk) => (
                <Link href="/brands" className="text-primary underline inline">
                  {chunk}
                </Link>
              ),
              br: () => <br />,
            })}
          </Text>
        </div>
      </div>
    </div>
  );
};
