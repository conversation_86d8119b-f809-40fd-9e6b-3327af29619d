interface Props {
  variant?: "default" | "secondary";
}
export default function DeliveryTruckIcon({ variant = "default" }: Props) {
  return variant === "default" ? (
    <svg
      width="49"
      height="26"
      viewBox="0 0 49 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="fill-current"
    >
      <path d="M29.7703 1.72623C29.6781 1.21942 29.2103 0.848633 28.6622 0.848633H3.39076C2.66246 0.848633 2.07274 1.40288 2.07274 2.08533C2.07274 2.76904 2.66246 3.32266 3.39076 3.32266H9.54093C10.2692 3.32266 10.859 3.877 10.859 4.56008C10.859 5.24316 10.2692 5.79741 9.54093 5.79741H1.31802C0.590393 5.79741 0 6.35165 0 7.03473C0 7.71782 0.590393 8.27134 1.31802 8.27134H12.5262C13.1693 8.27134 13.6911 8.76189 13.6911 9.36564C13.6911 9.96939 13.1693 10.4592 12.5262 10.4592H6.57146C5.84315 10.4592 5.25343 11.0136 5.25343 11.6966C5.25343 12.3796 5.84315 12.934 6.57146 12.934H10.9601C11.6038 12.934 12.1257 13.4238 12.1257 14.0275C12.1257 14.6312 11.6038 15.1211 10.9601 15.1211H4.90144C4.17313 15.1211 3.58341 15.6754 3.58341 16.3584C3.58341 17.0415 4.17313 17.5958 4.90144 17.5958H13.2289L13.7542 20.3847H16.7707C16.5933 20.7894 16.4921 21.2311 16.4921 21.6962C16.4921 23.605 18.14 25.152 20.1733 25.152C22.2057 25.152 23.8536 23.605 23.8536 21.6962C23.8536 21.2311 23.7525 20.7894 23.575 20.3847H33.1492L29.7703 1.72623ZM21.7573 21.6962C21.7573 22.5179 21.0485 23.1833 20.1732 23.1833C19.2973 23.1833 18.5883 22.5179 18.5883 21.6962C18.5883 21.1244 18.9354 20.6338 19.4393 20.3846H20.9063C21.4101 20.6338 21.7573 21.1243 21.7573 21.6962Z" />
      <path d="M48.9623 18.2172L47.4502 11.2147L41.9292 3.24023H31.877L34.9906 20.3841H38.6675C38.4908 20.7888 38.3895 21.2305 38.3895 21.6957C38.3895 23.6044 40.0375 25.1514 42.0699 25.1514C44.1032 25.1514 45.751 23.6044 45.751 21.6957C45.751 21.2305 45.6499 20.7888 45.4725 20.3841H47.0747C47.6527 20.3841 48.2001 20.1408 48.566 19.7205C48.9312 19.3011 49.0773 18.7487 48.9623 18.2172ZM37.567 6.43059H40.0853L43.3547 11.1516H38.4249L37.567 6.43059ZM43.6548 21.6957C43.6548 22.5174 42.9459 23.1829 42.0699 23.1829C41.1954 23.1829 40.4858 22.5174 40.4858 21.6957C40.4858 21.1239 40.8337 20.6334 41.3368 20.3841H42.8039C43.3076 20.6334 43.6548 21.1238 43.6548 21.6957Z" />
    </svg>
  ) : (
    <svg
      width="34"
      height="22"
      viewBox="0 0 34 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="fill-current"
    >
      <path d="M33.2848 11.3214C33.2848 11.316 33.2845 11.3106 33.2844 11.3024C33.2755 10.8942 32.9477 10.569 32.5346 10.569H32.3377L29.2971 4.69039C29.1688 4.44204 28.913 4.28759 28.6305 4.28751L23.4393 4.28643L23.6374 2.71659C23.6939 2.08924 23.4999 1.4969 23.0911 1.04855C22.6831 0.601132 22.1134 0.354675 21.4869 0.354675H5.37538C4.97028 0.354675 4.62615 0.668121 4.58753 1.07222L4.42603 2.36561H13.6056C14.2017 2.36561 14.6722 2.8488 14.6566 3.44486C14.641 4.04092 14.1451 4.52409 13.5491 4.52409H10.4872C10.4877 4.52564 10.4883 4.5268 10.4888 4.52952H1.49735C1.07583 4.52952 0.725157 4.87116 0.714133 5.29269C0.703111 5.71422 1.03582 6.05587 1.45735 6.05587H13.3611C13.9136 6.1411 14.3265 6.61884 14.3113 7.19739C14.2946 7.83278 13.7686 8.34795 13.1344 8.35327H4.92765C4.50189 8.35327 4.14774 8.69839 4.13656 9.12415C4.12538 9.54992 4.46149 9.89504 4.88725 9.89504H12.9973C13.558 9.97321 13.979 10.4549 13.9638 11.0391C13.947 11.6776 13.4157 12.1954 12.7771 12.1954H3.47306L2.6913 12.1962C2.26554 12.1962 1.9114 12.5414 1.90022 12.9671C1.88904 13.3929 2.22507 13.738 2.65091 13.738H3.32559L3.06172 16.4992C3.00513 17.1264 3.19909 17.7188 3.60797 18.1671C4.01592 18.6146 4.58568 18.861 5.21218 18.861H5.7942C6.07157 20.447 7.44375 21.6454 9.1222 21.6454C10.8007 21.6454 12.2356 20.447 12.5958 18.8611H19.859C20.4463 18.8611 21.0023 18.6366 21.4383 18.2689C21.837 18.6515 22.3655 18.8611 22.9426 18.8611H23.0621C23.3394 20.447 24.7115 21.6454 26.3901 21.6454C28.0685 21.6454 29.5035 20.447 29.8637 18.8611H30.3431C31.592 18.8611 32.6996 17.8457 32.8122 16.5977L33.282 11.3938C33.2826 11.3857 33.2823 11.3802 33.2827 11.3721C33.2835 11.3612 33.285 11.3504 33.2853 11.3401C33.2854 11.3346 33.2848 11.3268 33.2848 11.3214ZM9.1626 20.1034C8.09994 20.1034 7.25797 19.239 7.2858 18.1763C7.31363 17.1135 8.20084 16.249 9.26351 16.249C10.3262 16.249 11.1681 17.1135 11.1402 18.1763C11.1124 19.239 10.2253 20.1034 9.1626 20.1034ZM26.4305 20.1034C25.3678 20.1034 24.5259 19.239 24.5537 18.1763C24.5815 17.1135 25.4687 16.249 26.5314 16.249C27.5941 16.249 28.436 17.1135 28.4081 18.1763C28.3803 19.239 27.4931 20.1034 26.4305 20.1034ZM31.2763 16.499C31.2362 16.9436 30.8274 17.3192 30.3836 17.3192H29.865C29.5217 15.8195 28.1888 14.7072 26.5717 14.7072C24.9546 14.7072 23.5635 15.8195 23.1417 17.3192H22.983C22.787 17.3192 22.6122 17.2456 22.4905 17.1121C22.368 16.9777 22.3103 16.795 22.3281 16.5974L23.3001 5.82882L25.5325 5.82928L25.1787 9.74939C25.122 10.3767 25.3159 10.969 25.7247 11.4173C26.1327 11.8649 26.7026 12.1115 27.3292 12.1115H31.6722L31.2763 16.499Z" />
    </svg>
  );
}
