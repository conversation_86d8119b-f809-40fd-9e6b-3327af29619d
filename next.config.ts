import type { NextConfig } from "next";
import createNextIntlPlugin from "next-intl/plugin";

const withNextIntl = createNextIntlPlugin();

const nextConfig: NextConfig = {
  /* config options here */
  env: {
    BACKEND_ADDRESS: "https://api-akal.tdg.tn",
    PRIVATE_ACCESS_BACKEND_ADDRESS: "https://www.test.tawer.tn",
    FRONTEND_DOMAIN_NAME: "akal.tn",
    GOOGLE_CLIENT_ID:
      "175357773537-cdkl8p3vn9uuuark4jaq322imf18ec7c.apps.googleusercontent.com",
    MEASUREMENT_ID: "",
    NEXT_PUBLIC_ENV: "preprod",
  },
  images: {
    domains: ["api-akal.tdg.tn"],
  },
  logging: {
    fetches: {
      fullUrl: true,
    },
  },
  async rewrites() {
    return [
      {
        source: "/produits",
        destination: "/produits/filtres",
      },
    ];
  },
};

module.exports = withNextIntl(nextConfig);
