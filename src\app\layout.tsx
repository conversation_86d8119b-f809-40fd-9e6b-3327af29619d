import type { <PERSON>ada<PERSON> } from "next";
import { DM_Sans } from "next/font/google";
import localFont from "next/font/local";
import "./globals.css";
import { getMessages } from "next-intl/server";
import { NextIntlClientProvider } from "next-intl";
import ReactQueryProvider from "@/utils/react-query-provider";
import Footer from "@/components/footer";
import Navbar from "@/components/navbar";
import { GoUpButton } from "@/components/go-up-button";
import { Toaster } from "@/components/ui/toaster";
import PromotionalBanner from "@/components/promotional-banner";
import Script from "next/script";
import { GoogleAnalytics } from "@next/third-parties/google";
import { retrieveCategoriesFromServerSide } from "@/modules/catalog/services/categories/categories-extraction";

const dmSans = DM_Sans({
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  style: ["normal", "italic"],
  display: "swap",
  variable: "--font-dm-sans",
});

const swell = localFont({
  src: [
    {
      path: "../../public/fonts/swell-regular.otf",
      weight: "400",
      style: "normal",
    },
  ],
  display: "swap", // Optional: improves performance by swapping fonts after they load
  variable: "--font-swell",
});

export const metadata: Metadata = {
  title: "Akal - Outils de jardinage - Faites de votre jardin un havre de paix",
  description:
    "Outils de jardinage de qualité chez Akal. Tout ce qu’il vous faut pour créer et entretenir un jardin magnifique.",
  icons: {
    icon: "/akal.ico",
  },
  robots:
    process.env.NEXT_PUBLIC_ENV === "preprod"
      ? {
          index: false,
          follow: false,
        }
      : undefined,
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const categories = await retrieveCategoriesFromServerSide();
  const messages = await getMessages();

  return (
    <html lang="en">
      <head>
        <Script
          id="facebook-meta-pixel"
          dangerouslySetInnerHTML={{
            __html: `
    !function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t,s)}(window, document,'script',
    'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', '1090239879467500');
    fbq('track', 'PageView');`,
          }}
        />
        <noscript>
          <img
            height="1"
            width="1"
            style={{ display: "none" }}
            src="https://www.facebook.com/tr?id=1090239879467500&ev=PageView&noscript=1"
          />
        </noscript>
      </head>
      <body
        className={`${swell.variable} ${dmSans.variable} ${dmSans.className} antialiased flex flex-col items-center overlfow-y-auto scrollbar-track-black scrollbar-thumb-primary`}
      >
        <GoogleAnalytics gaId={process.env.MEASUREMENT_ID as string} />
        <NextIntlClientProvider messages={messages}>
          <ReactQueryProvider>
            <Navbar categories={categories || []} />
            <>
              {
                <div className="2extraL:mt-[140px] mt-[130px] flex-1 w-full max-w-full">
                  {children}
                </div>
              }
              <PromotionalBanner />
              <Footer />
              <div className="fixed regularL:bottom-20 bottom-20 right-5 z-[9999]">
                <GoUpButton />
              </div>
            </>
          </ReactQueryProvider>
        </NextIntlClientProvider>
        <Toaster />
      </body>
    </html>
  );
}
