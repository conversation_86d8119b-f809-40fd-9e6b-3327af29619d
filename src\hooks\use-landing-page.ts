import getLandingPageContent from "@/services/page-palette/landing-page";
import { LandingPageContent } from "@/types";
import { useQuery } from "@tanstack/react-query";

export default function useLandingPage() {
  const { data, isLoading, isError } = useQuery<LandingPageContent | null>({
    queryKey: ["landing-page"],
    queryFn: getLandingPageContent,
  });

  return {
    isLoading,
    landingPageContent: data,
  };
}
