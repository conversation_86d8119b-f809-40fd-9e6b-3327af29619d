import useProducts from "@/modules/catalog/hooks/products/use-products";
import { useCartStore } from "../store/cart-store";

interface Props {
  limit?: number;
  similarProductSlug?: string;
}

export default function useCartSimilarProducts({
  limit = 4,
  similarProductSlug,
}: Props) {
  const { cartItems } = useCartStore((store) => store.state);

  const { products, productsAreLoading } = useProducts({
    limit: limit,
    similarProductSlug,
    queryKeys: [cartItems.length],
  });

  const filteredProducts = products
    ? products.filter((product) => {
        return !cartItems.some((item) => item.productId === product.id);
      })
    : [];

  return {
    products: filteredProducts,
    productsAreLoading,
  };
}
