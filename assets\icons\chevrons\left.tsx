interface Props {
  color?: string;
  width?: number;
  height?: number;
}

export default function ChevronLeft({
  color = "black",
  height = 17,
  width = 10,
}: Props) {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 10 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M2.85379 8.70963L9.04471 2.52357C9.50258 2.06571 9.50258 1.32533 9.04471 0.872337C8.58685 0.414471 7.84647 0.419342 7.38861 0.872337L0.374499 7.88157C-0.0687535 8.32483 -0.0784951 9.03598 0.340403 9.49384L7.38373 16.5518C7.61267 16.7807 7.91466 16.8928 8.21179 16.8928C8.50891 16.8928 8.81091 16.7807 9.03984 16.5518C9.49771 16.0939 9.49771 15.3535 9.03984 14.9006L2.85379 8.70963Z"
        fill={color}
      />
    </svg>
  );
}
