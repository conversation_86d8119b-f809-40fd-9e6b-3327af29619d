interface Props {
  color?: string;
}
export default function ProfileIcon({ color = "#3A8B60" }: Props) {
  return (
    <svg
      width="16"
      height="20"
      viewBox="0 0 16 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="fill-current"
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.1875 4.9585C3.1875 2.30062 5.34213 0.145996 8 0.145996C10.6579 0.145996 12.8125 2.30062 12.8125 4.9585C12.8125 7.61637 10.6579 9.771 8 9.771C5.34213 9.771 3.1875 7.61637 3.1875 4.9585Z"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M0.894531 16.0239C0.894531 13.33 3.07848 11.146 5.77251 11.146H10.2249C12.9189 11.146 15.1029 13.33 15.1029 16.0239C15.1029 18.1394 13.388 19.8543 11.2725 19.8543H4.72489C2.60944 19.8543 0.894531 18.1394 0.894531 16.0239Z"
      />
    </svg>
  );
}
