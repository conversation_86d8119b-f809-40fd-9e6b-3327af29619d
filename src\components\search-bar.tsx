import { Search, X } from "lucide-react";
import { useTranslations } from "next-intl";
import { useState, KeyboardEvent, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Text from "@/styles/text-styles";
import useProducts from "../modules/catalog/hooks/products/use-products";
import SearchProductContainer from "../modules/catalog/components/products/product/container/search";
import {
  TopDrawer,
  TopDrawerClose,
  TopDrawerContent,
  TopDrawerTitle,
  TopDrawerTrigger,
} from "@/components/ui/top-drawer";
import useCategories from "../modules/catalog/hooks/categories/use-categories";
import useScreenSize from "@/hooks/use-screen-size";

export default function SearchDrawer() {
  const t = useTranslations("shared.navbar.appHeader.searchBar");
  const [open, setOpen] = useState(false);
  const [search, setSearch] = useState("");
  const router = useRouter();
  const { products, productsAreLoading } = useProducts({
    limit: 8,
    search: search.trim(),
  });
  const { categories } = useCategories();

  const { width } = useScreenSize();
  const isMobile = width < 768;

  useEffect(() => {
    if (!open) {
      setSearch("");
    }
  }, [open]);

  const handleSearchSubmit = () => {
    if (search.trim() !== "") {
      setOpen(false);
      router.push(`/produits/filtres?search=${search}`);
      setSearch("");
    }
  };

  const handleKeyDown = (event: KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter" && search.trim() !== "") {
      handleSearchSubmit();
    }
  };

  return (
    <div className="w-full max-w-[550px]">
      <TopDrawer open={open} onOpenChange={setOpen}>
        <TopDrawerTrigger asChild>
          {isMobile ? (
            <Button
              variant="ghost"
              size="icon"
              className="text-primary bg-gray/10 rounded-full hover:bg-gray/20 transition-colors"
              aria-label="Search"
            >
              <Search size={22} />
            </Button>
          ) : (
            <div className="flex items-center gap-3 mx-auto max-w-[800px] ">
              <div className="relative flex-1">
                <Input
                  placeholder={t("placeholder")}
                  className="w-full border border-gray-300 rounded-full py-3 px-5 h-12  focus:border-primary focus:ring-1 focus:ring-primary"
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  onKeyDown={handleKeyDown}
                />
              </div>
              <Button
                onClick={handleSearchSubmit}
                className="rounded-full border-none hover:border-none bg-blue text-white hover:bg-blue/90 hover:text-white hover:border-blue h-12 px-6 font-bold"
              >
                <Text textStyle="TS6" className="font-bold">
                  {t("search")}
                </Text>
              </Button>
            </div>
          )}
        </TopDrawerTrigger>
        <TopDrawerContent className="p-6 h-[90vh] max-h-[600px] overflow-hidden flex flex-col justify-start">
          <TopDrawerTitle className="sr-only">{t("title")}</TopDrawerTitle>
          <div className="w-full h-full flex-1 flex flex-col">
            <div className="flex justify-between items-center mb-6">
              <div className="w-full text-center relative">
                <Text textStyle="TS2" className="text-black font-bold">
                  {t("title")}
                </Text>
                <TopDrawerClose asChild>
                  <Button
                    variant="ghost"
                    className="p-0 h-auto absolute right-0 top-1/2 transform -translate-y-1/2"
                    aria-label="Close"
                    onClick={() => {
                      setSearch("");
                      setOpen(false);
                    }}
                  >
                    <X size={24} />
                  </Button>
                </TopDrawerClose>
              </div>
            </div>

            <div className="flex items-center gap-3 mb-6 max-w-3xl mx-auto w-full">
              <div className="relative flex-1">
                <Input
                  placeholder={t("placeholder")}
                  className="w-full border border-gray-300 rounded-full py-3 px-5 h-12 text-base focus:border-primary focus:ring-1 focus:ring-primary"
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  onKeyDown={handleKeyDown}
                />
              </div>
              <Button
                onClick={handleSearchSubmit}
                className="rounded-full bg-blue text-white hover:bg-blue/90 hover:text-white hover:border-blue h-12 px-6 font-bold"
              >
                {t("search")}
              </Button>
            </div>

            {/* Scrolled area */}
            {search.trim() !== "" && (
              <div className="flex-1 max-h-[50vh] w-full overflow-y-auto">
                <div className="w-full py-2 flex flex-col space-y-4 max-w-7xl mx-auto pb-4">
                  {search.trim() !== "" && (
                    <Text
                      textStyle="TS5"
                      className="font-medium mb-3 text-gray-700"
                    >
                      {t("suggestions")}
                    </Text>
                  )}

                  {productsAreLoading ? (
                    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4  gap-6 px-2">
                      {Array.from({ length: 5 }).map((_, idx) => (
                        <SearchProductContainer key={idx} product={null} />
                      ))}
                    </div>
                  ) : products && products.length > 0 ? (
                    <>
                      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6 px-2 ">
                        {products.slice(0, 5).map((product) => (
                          <SearchProductContainer
                            key={product.id}
                            product={product}
                            onClick={() => {
                              setOpen(false);
                            }}
                          />
                        ))}
                      </div>
                    </>
                  ) : (
                    <div className="bg-gray-50 rounded-lg p-8">
                      <Text
                        textStyle="TS5"
                        className="text-gray font-medium min-h-[100px] flex justify-center items-center text-center"
                      >
                        {t("noResults")}
                      </Text>
                    </div>
                  )}

                  {search.trim() !== "" &&
                    !productsAreLoading &&
                    products &&
                    products.length > 5 && (
                      <div className="w-full flex justify-center mt-6">
                        <Button
                          variant="voirplus"
                          className="px-8 py-3 text-base font-medium"
                          onClick={handleSearchSubmit}
                        >
                          {t("seeMore") ||
                            `View all ${products.length} results`}
                        </Button>
                      </div>
                    )}
                </div>
              </div>
            )}
          </div>
        </TopDrawerContent>
      </TopDrawer>
    </div>
  );
}
