import { GET } from "@/lib/http-methods";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { CustomError } from "@/utils/custom-error";
import { AxiosError } from "axios";
import { WishedProductInResponse } from "../types/products";
import castToWishedItem from "../utils/types-casting/wishlist-items";

export async function extractWishlistItems() {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = await GET("/favourites/products", headers);
    const wishedItems = (res.data as WishedProductInResponse[]).map(
      (cartItem) => castToWishedItem(cartItem)
    );

    return wishedItems || [];
  } catch (error) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.status == 401) {
      const res = await refreshToken(extractWishlistItems);
      if (!res) throw new CustomError("Unauthorized", 401);

      return res;
    } else throw new CustomError("Server Error!", 500);
  }
}
