import {
  UserSignUpType,
  UserDataType,
  UserResponseDataType,
} from "@auth/types";

export function castToUserType(
  userInResponse: UserResponseDataType
): UserDataType {
  return {
    email: userInResponse.email,
    name: userInResponse.name,
    isAuthenticated: true,
    role: "",
    points: userInResponse.points ? parseInt(userInResponse.points, 10) : 0,
  };
}

export function castToSignUpServerSideType(data: UserSignUpType) {
  return {
    email: data.email,
    name: `${data.firstName} ${data.lastName}`,
    password: data.password,
  };
}
