import { persist } from "zustand/middleware";
import { create } from "zustand";
import { WishedProductItemType } from "../types/products";
import { useEffect, useState } from "react";
import useUserStore from "@/modules/auth/store/user-store";
import { CustomError } from "@/utils/custom-error";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { addWishedItemOnServerSide } from "../services/item-addition";
import { deleteWishedItemOnServerSide } from "../services/item-deletion";
import { clearWishlistOnServerSide } from "../services/item-deletion";
import { extractWishlistItems } from "../services/items-extraction";
import { logger } from "@/utils/logger";

type State = {
  wishlistOnServerSideVersion: number;
  wishlistItems: WishedProductItemType[];
};

type Action = {
  addProductItem: (
    p: WishedProductItemType,
    wishlistAnimation?: boolean,
    checkAuthentication?: boolean
  ) => void;
  removeProductItem: (id: string, checkAuthentication?: boolean) => void;
  toggleProductItem: (p: WishedProductItemType) => void;
  emptyWishlist: () => void;
  setWishlist: (wishlistItems: WishedProductItemType[]) => void;
  setWishlistOnServerSideVersion: (version: number) => void;
  moveAllToCart: () => Promise<{ success: boolean; error?: any }>;
  isProductInWishlist: (productItemId: string) => boolean;
};

export const useWishlistStore = create(
  persist<{ state: State; actions: Action }>(
    (set) => ({
      state: {
        wishlistItems: [],
        wishlistOnServerSideVersion: 0,
      },
      actions: {
        setWishlistOnServerSideVersion: (version) =>
          set(({ state }) => ({
            state: {
              wishlistOnServerSideVersion: version,
              wishlistItems: state.wishlistItems,
            },
          })),
        addProductItem: (p, checkAuthentication = true) =>
          set(({ state }) => {
            //product doesn't exist and will be added to the wishlist
            //checking if use is authenticated to store product on server side also
            const { user } = useUserStore.getState();

            if (checkAuthentication && user && user.isAuthenticated) {
              //creating product item on the server side also

              addWishedItemOnServerSide({
                id: p.productItemId,
              })
                .then((_) => {
                  useWishlistStore
                    .getState()
                    .actions.setWishlistOnServerSideVersion(
                      state.wishlistOnServerSideVersion + 1
                    );
                })
                .catch(() => {
                  useWishlistStore
                    .getState()
                    .actions.removeProductItem(p.productItemId, false);
                });
            }

            return {
              state: {
                wishlistItems: [p, ...state.wishlistItems],
                wishlistOnServerSideVersion: state.wishlistOnServerSideVersion,
              },
            };
          }),
        removeProductItem: (productItemId, checkAuthentication = true) =>
          set(({ state }) => {
            const { user } = useUserStore.getState();

            if (checkAuthentication && user && user.isAuthenticated) {
              const productItemToDelete = state.wishlistItems.find(
                (productItem) => productItem.productItemId === productItemId
              );

              if (productItemToDelete) {
                deleteWishedItemOnServerSide(productItemToDelete.id)
                  .then(() => {
                    // On successful deletion, increment version to trigger refetch
                    useWishlistStore
                      .getState()
                      .actions.setWishlistOnServerSideVersion(
                        state.wishlistOnServerSideVersion + 1
                      );
                  })
                  .catch((error: CustomError) => {
                    // Only restore item if it's not a 404 (item doesn't exist on server)
                    if (error.status !== 404 && productItemToDelete) {
                      useWishlistStore
                        .getState()
                        .actions.addProductItem(
                          productItemToDelete,
                          false,
                          false
                        );
                    }
                  });
              }
            }

            return {
              state: {
                wishlistItems: state.wishlistItems.filter(
                  (productItem) => productItem.productItemId !== productItemId
                ),
                wishlistOnServerSideVersion: state.wishlistOnServerSideVersion,
              },
            };
          }),
        emptyWishlist: () =>
          set(({ state }) => ({
            state: {
              wishlistItems: [],
              wishlistOnServerSideVersion: state.wishlistOnServerSideVersion,
            },
          })),
        setWishlist: (wishlistItems) =>
          set(({ state }) => ({
            state: {
              wishlistItems,
              wishlistOnServerSideVersion: state.wishlistOnServerSideVersion,
            },
          })),
        toggleProductItem: (p) =>
          set(({ state }) => {
            const isInWishlist = state.wishlistItems.some(
              (item) => item.productItemId === p.productItemId
            );

            if (isInWishlist) {
              // Remove from wishlist
              const { user } = useUserStore.getState();
              const existingItem = state.wishlistItems.find(
                (item) => item.productItemId === p.productItemId
              );

              if (user && user.isAuthenticated && existingItem) {
                deleteWishedItemOnServerSide(existingItem.id)
                  .then(() => {
                    // On successful deletion, increment version to trigger refetch
                    useWishlistStore
                      .getState()
                      .actions.setWishlistOnServerSideVersion(
                        state.wishlistOnServerSideVersion + 1
                      );
                  })
                  .catch((error: CustomError) => {
                    // Only restore item if it's not a 404 (item doesn't exist on server)
                    if (error.status !== 404) {
                      useWishlistStore
                        .getState()
                        .actions.addProductItem(p, false, false);
                    }
                  });
              }

              return {
                state: {
                  wishlistItems: state.wishlistItems.filter(
                    (item) => item.productItemId !== p.productItemId
                  ),
                  wishlistOnServerSideVersion:
                    state.wishlistOnServerSideVersion,
                },
              };
            } else {
              // Add to wishlist
              const { user } = useUserStore.getState();

              if (user && user.isAuthenticated) {
                addWishedItemOnServerSide({ id: p.productItemId })
                  .then((_) => {
                    useWishlistStore
                      .getState()
                      .actions.setWishlistOnServerSideVersion(
                        state.wishlistOnServerSideVersion + 1
                      );
                  })
                  .catch(() => {
                    useWishlistStore
                      .getState()
                      .actions.removeProductItem(p.productItemId, false);
                  });
              }

              return {
                state: {
                  wishlistItems: [p, ...state.wishlistItems],
                  wishlistOnServerSideVersion:
                    state.wishlistOnServerSideVersion,
                },
              };
            }
          }),
        isProductInWishlist: (productItemId: string): boolean => {
          return useWishlistStore
            .getState()
            .state.wishlistItems.some(
              (item: WishedProductItemType) =>
                item.productItemId === productItemId
            );
        },
        moveAllToCart: async () => {
          const { state } = useWishlistStore.getState();
          const { useCartStore } = await import(
            "@/modules/cart/store/cart-store"
          );
          const { addCartItemsOnServerSide } = await import(
            "@/modules/cart/services/cart-items-addition"
          );
          const { addProductItem } = useCartStore.getState().actions;
          const { user } = useUserStore.getState();

          if (state.wishlistItems.length === 0) {
            return { success: true };
          }

          try {
            for (const item of state.wishlistItems) {
              addProductItem(
                {
                  slug: item.slug,
                  id: item.productItemId,
                  productId: item.productId,
                  cartQuantity: 1,
                  name: item.name,
                  prices: item.prices,
                  image: item.image,
                  variations: item.variations,
                  inStock: item.inStock,
                },
                false,
                false
              );
            }

            // Clear wishlist locally
            useWishlistStore.getState().actions.emptyWishlist();

            // Sync with server if user is authenticated
            if (user && user.isAuthenticated) {
              try {
                const cartItems = state.wishlistItems.map((item) => ({
                  id: item.productItemId,
                  quantity: 1,
                }));
                await addCartItemsOnServerSide(cartItems);

                const wishlistItemIds = state.wishlistItems.map(
                  (item) => item.id
                );
                await clearWishlistOnServerSide(wishlistItemIds);

                useCartStore
                  .getState()
                  .actions.setCartOnServerSideVersion(
                    useCartStore.getState().state.cartOnServerSideVersion + 1
                  );
                useWishlistStore
                  .getState()
                  .actions.setWishlistOnServerSideVersion(
                    state.wishlistOnServerSideVersion + 1
                  );
              } catch (serverError) {
                logger.error("Error syncing with server:", serverError);
              }
            }

            return { success: true };
          } catch (error) {
            logger.error("Error moving wishlist items to cart:", error);
            return { success: false, error };
          }
        },
      },
    }),
    {
      name: "wishlist",
      partialize: (store) =>
        ({ state: store.state } as { state: State; actions: Action }),
    }
  )
);

export const useWishlist = () => {
  const [hasHydrated, setHasHydrated] = useState(false);
  const store = useWishlistStore();
  const { user } = useUserStore();
  const { data } = useQuery({
    queryKey: ["wishlistItems", user, store.state.wishlistOnServerSideVersion],
    queryFn: extractWishlistItems,
    enabled: user !== null && user.isAuthenticated,
    placeholderData: keepPreviousData,
  });

  useEffect(() => {
    setHasHydrated(true);
  }, []);

  //fetching user data if user is authenticated
  useEffect(() => {
    if (user && user.isAuthenticated && data) store.actions.setWishlist(data);
  }, [user, data, store.actions]);

  return {
    wishlistItems: hasHydrated ? store.state.wishlistItems : [],
    addProductItem: store.actions.addProductItem,
    removeProductItem: store.actions.removeProductItem,
    toggleProductItem: store.actions.toggleProductItem,
    isProductInWishlist: store.actions.isProductInWishlist,
    emptyWishlist: store.actions.emptyWishlist,
    moveAllToCart: store.actions.moveAllToCart,
  };
};
