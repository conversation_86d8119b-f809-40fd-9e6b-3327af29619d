"use client";
import * as React from "react";
import * as ProgressPrimitive from "@radix-ui/react-progress";

import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";

type ProgressProps = {
  progressbarclassname?: string;
  progressindicatorclassname?: string;
} & React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>;

const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  ProgressProps
>(({ className, value, ...props }, ref) => (
  <div className="relative">
    <ProgressPrimitive.Root
      ref={ref}
      className={cn(
        "relative overflow-hidden h-2 w-full rounded-full bg-gray/20 dark:bg-neutral-50/20",
        className
      )}
      {...props}
    >
      <ProgressPrimitive.Indicator
        className={cn(
          "relative flex items-center h-full w-full flex-1 bg-gradient-to-r from-primary from-[40%] to-secondary transition-all dark:bg-neutral-50",
          props.progressbarclassname
        )}
        style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
      />
    </ProgressPrimitive.Root>
    {
      <div
        className={cn(
          "rounded-[11px] bg-secondary text-white py-1 px-2 -top-3 absolute",
          props.progressindicatorclassname
        )}
        style={{ left: `calc(${value || 0}% - 30px)` }}
      >
        <Text textStyle="TS7">{`${(value || 0).toFixed(0)}%`}</Text>
      </div>
    }
  </div>
));
Progress.displayName = ProgressPrimitive.Root.displayName;

export { Progress };
