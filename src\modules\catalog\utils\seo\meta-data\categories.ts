import { metadata } from "@/app/layout";
import { retrieveCategoryFromServerSide } from "@/modules/catalog/services/categories/category-extraction";
import { AxiosError } from "axios";
import { Metadata } from "next";
import { notFound } from "next/navigation";

interface Params {
  categorySlug: string;
}

export async function generateCategoryMetadata({
  categorySlug,
}: Params): Promise<Metadata> {
  try {
    const category = await retrieveCategoryFromServerSide({ categorySlug });

    if (category?.metaContent) {
      return {
        title: category.metaContent.title,
        description: category.metaContent.description,
        keywords: category.metaContent.keywords,
      };
    } else return metadata;
  } catch (error) {
    const axiosError = error as AxiosError;

    if (axiosError?.response?.status === 404) notFound(); //category not found

    return metadata;
  }
}
