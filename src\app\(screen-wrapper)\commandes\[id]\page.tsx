import { cn } from "@/lib/utils";
import OrderConfirmation from "@/modules/checkout/components/orders/order-confirmation";

interface Props {
  params: Promise<{
    id: string;
  }>;
}

export default async function Page(props: Props) {
  const orderId = (await props.params).id;
  
  return (
    <div className={cn("w-full flex flex-col gap-8")}>
      <OrderConfirmation orderId={orderId} />
    </div>
  );
}
