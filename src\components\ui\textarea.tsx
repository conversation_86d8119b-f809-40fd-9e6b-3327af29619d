import * as React from "react";

import { cn } from "@/lib/utils";
import { TextStyle } from "@/styles/text-styles";

const Textarea = React.forwardRef<
  HTMLTextAreaElement,
  React.ComponentProps<"textarea">
>(({ className, ...props }, ref) => {
  return (
    <textarea
      className={cn(
        "flex min-h-[60px] w-full bg-transparent rounded-[15px] border-gray px-3 py-1 text-base outline-none shadow-sm placeholder:text-gray disabled:cursor-not-allowed disabled:opacity-50 md:text-sm ",
        className,
        TextStyle["TS7"]
      )}
      ref={ref}
      {...props}
    />
  );
});
Textarea.displayName = "Textarea";

export { Textarea };
