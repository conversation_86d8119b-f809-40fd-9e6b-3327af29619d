import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import WarnInput from "@/components/input/warn-input";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { FormEvent, RefObject } from "react";
import Link from "next/link";

interface AuthInputPropsType {
  formRef: RefObject<HTMLFormElement | null>;
  authType: "signIn" | "signUp";
  warning: {
    generalWarning: string;
    email: string;
    firstName?: string;
    lastName?: string;
    password: string;
  };
  submitInfo: (event: FormEvent) => void;
  isLoading: boolean;
}
export default function AuthInput({
  authType,
  warning,
  submitInfo,
  isLoading,
  formRef,
}: AuthInputPropsType) {
  const t = useTranslations("shared.auth");
  return (
    <form ref={formRef} className={"flex flex-col space-y-8 w-full text-black"}>
      {warning.generalWarning !== "" ? (
        <Text textStyle="TS6" className="text-danger">
          {warning.generalWarning}
        </Text>
      ) : null}

      {authType === "signUp" && (
        <div className="flex-1 flex space-x-2 w-full ">
          <div className="basis-1/2 space-y-2">
            <label>
              <Text textStyle="TS6" className="text-gray">
                {t("input.firstName")}
              </Text>
            </label>
            <WarnInput
              id="firstName"
              name="firstName"
              warning={warning.firstName as string}
              className="h-[56px] border border-primary text-base"
            />
          </div>
          <div className="basis-1/2 space-y-2">
            <label>
              <Text textStyle="TS6" className="text-gray">
                {t("input.lastName")}
              </Text>
            </label>
            <WarnInput
              id="lastName"
              name="lastName"
              warning={warning.lastName as string}
              className="w-full h-[56px] border border-primary text-base"
            />
          </div>
        </div>
      )}

      <div className="space-y-2">
        <label>
          <Text textStyle="TS6" className="text-gray">
            {t("input.email")}
          </Text>
        </label>

        <WarnInput
          id="email"
          name="email"
          warning={warning.email}
          className="flex-1 h-[56px] border border-primary text-base"
        />
      </div>

      <div className="space-y-2">
        <label>
          <Text textStyle="TS6" className="text-gray">
            {t("input.password")}
          </Text>
        </label>

        <WarnInput
          id="password"
          name="password"
          warning={warning.password}
          type="password"
          className="flex-1 h-[56px] border border-primary  text-base"
          eyeColor="#254F84"
        />

        <Link href="/auth/reset-password">
          <Text textStyle="TS7" className="text-primary">
            {t.raw("resetPassword")}
          </Text>
        </Link>
      </div>

      <Button
        className={cn(
          "w-full h-[56px] bg-primary text-white rounded-lg text-base font-semibold mt-6",
          {
            "opacity-70": isLoading,
          }
        )}
        disabled={isLoading}
        onClick={submitInfo}
      >
        <Text textStyle="TS7">Se connecter</Text>
      </Button>
    </form>
  );
}
