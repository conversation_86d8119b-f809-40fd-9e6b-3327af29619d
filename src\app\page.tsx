import HeroSection from "@/components/hero-section";
import { screenWrapperStyle } from "@/components/screen-wrapper";
import { cn } from "@/lib/utils";
import ProductsOverview from "@/modules/catalog/components/products/overview/default";
import getLandingPageContent from "@/services/page-palette/landing-page";
import { Metadata } from "next";
import { metadata } from "./layout";
import CategoriesOverview from "@/modules/catalog/components/categories/categories-overview";
import PromotionsOverview from "@/modules/catalog/components/promotions";
import BrandsOverview from "@/modules/catalog/components/brands/brands-overview";
import CustomersReviews from "@/components/reviews/customers-reviews";
import Link from "next/link";
import { getProductPageUrl } from "@/modules/catalog/utils/urls";
import retrieveCategoriesProductsFromServerSide from "@/modules/catalog/utils/categories-products-extraction";

export const revalidate = 0; //recaching each 10 minutes

export async function generateMetadata(): Promise<Metadata> {
  const landingPageContent = await getLandingPageContent();

  if (landingPageContent?.metaContent) {
    return {
      title: landingPageContent.metaContent.title,
      description: landingPageContent.metaContent.description,
      keywords: landingPageContent.metaContent.keywords,
      metadataBase: new URL(
        `https://${process.env.FRONTEND_DOMAIN_NAME as string}`
      ),
      alternates: {
        canonical: "/",
      },
    };
  }

  return metadata;
}

export default async function Home() {
  const products = await retrieveCategoriesProductsFromServerSide({});

  return (
    <>
      <main className="2extraL:mt-0 -mt-[50px] w-full flex flex-col items-center justify-center">
        <HeroSection className="w-full" />
        <CategoriesOverview className={cn("L:mt-12", screenWrapperStyle)} />
        <div className={cn("w-full bg-[#EFF0F2] flex justify-center")}>
          <div className={cn(screenWrapperStyle)}>
            <div className="z-20 relative flex flex-col md:space-y-16 space-y-8">
              <PromotionsOverview />
              <ProductsOverview variant="mostSold" />
              <ProductsOverview />
              <BrandsOverview />
              <CustomersReviews />
            </div>
          </div>
        </div>
      </main>

      {/* making products crawlable for seo purposes */}
      <div className="max-w-0 max-h-0 overflow-hidden">
        {products.map((product, idx) => (
          <a key={idx} href={getProductPageUrl(product.slug)} className="">
            {product.name}
          </a>
        ))}

        <Link href="/marques" className="">
          Marques
        </Link>
      </div>
    </>
  );
}
