"use client";
import useMenu from "@/hooks/use-menu";
import TopNotificationBar from "./top-notification-bar";
import AppHeader from "./app-header";
import { Suspense } from "react";
import { CategoryType } from "@/modules/catalog/types/categories";
import MobileBottomNavbar from "./mobile-bottom-navbar";
import CategoriesMobileMenu from "./categories-mobile-menu";
import CategoriesMenu from "./categories-menu";

interface Props {
  categories: CategoryType[];
}

export default function Navbar({ categories }: Props) {
  useMenu();

  return (
    <>
      <header
        id="navbar-container"
        className="w-full mx-0 fixed right-0 left-0 z-50 bg-white"
      >
        <TopNotificationBar />
        <Suspense>
          <AppHeader />
        </Suspense>
        <CategoriesMenu categories={categories} />
      </header>

      <div className="menu-overlay"></div>

      {/* Mobile Menu */}
      <nav className="mobile-menu">
        <CategoriesMobileMenu categories={categories} />
      </nav>

      <MobileBottomNavbar />
    </>
  );
}
