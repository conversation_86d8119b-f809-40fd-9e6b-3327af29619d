import { cn } from "@/lib/utils";

interface Props {
  current: number;
  count: number;
}

export default function CarouselIndicator(props: Props) {
  return (
    <div className="flex gap-2">
      {new Array(props.count).fill("").map((_, idx) => (
        <span
          key={idx}
          className={cn(
            "h-2.5 rounded-2xl transition-all content-[''] w-2.5 bg-blue border border-blue",
            {
              " bg-blue": idx + 1 === props.current,
              " bg-transparent": idx + 1 !== props.current,
            }
          )}
        />
      ))}
    </div>
  );
}
