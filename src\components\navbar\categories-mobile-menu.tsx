"use client";

import type { CategoryType } from "@/modules/catalog/types/categories";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../ui/accordion";
import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import Link from "next/link";
import {
  getCategoryPageUrl,
  getSubCategoryPageUrl,
  getSubSubCategoryPageUrl,
} from "@/modules/catalog/utils/urls";
import { ChevronRight, X, Grid3X3, ExternalLink } from "lucide-react";
import { useTranslations } from "next-intl";
import { companyPhoneNumber } from "@/data";

interface Props {
  categories: CategoryType[];
}

export default function CategoriesMobileMenu({ categories }: Props) {
  const t = useTranslations("shared.navbar");

  const closeMobileMenu = () => {
    const menu = document.querySelector(".mobile-menu");
    const overlay = document.querySelector(".menu-overlay");
    if (menu && overlay) {
      menu.classList.remove("active");
      overlay.classList.remove("active");
      document.body.style.overflow = "";
    }
  };

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Enhanced Header */}
      <div className="flex items-center justify-between p-6 bg-gradient-to-r from-slate-50 to-gray-50 border-b border-gray-100 shadow-sm">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Grid3X3 className="w-5 h-5 text-primary" />
          </div>
          <Text textStyle="TS3" className="font-bold text-gray-900">
            {t("mobileMenu.categories")}
          </Text>
        </div>
        <button
          className="mobile-menu-close w-10 h-10 bg-white rounded-full flex items-center justify-center hover:bg-gray-50 transition-all duration-200 shadow-sm border border-gray-200 hover:border-gray-300"
          aria-label={t("mobileMenu.close")}
          onClick={closeMobileMenu}
        >
          <X className="w-5 h-5 text-gray-600" />
        </button>
      </div>

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-4">
          <Accordion type="single" collapsible className="w-full space-y-2">
            <div className=" bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
              <Link
                href={"/produits/filtres"}
                className="w-full px-4 py-4 flex items-center justify-between hover:bg-gray-50 transition-colors duration-200 group"
                onClick={closeMobileMenu}
              >
                <Text
                  textStyle="TS4"
                  className="font-semibold text-gray-900 group-hover:text-primary transition-colors"
                >
                  {t("allProducts")}
                </Text>
                <ChevronRight
                  size={18}
                  className="shrink-0 transition-all duration-200 text-gray-400 group-hover:text-primary group-hover:translate-x-1"
                />
              </Link>
            </div>
            {categories && categories.length > 0 ? (
              categories.map((category, idx) => (
                <AccordionItem
                  className=" bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden"
                  value={category.id}
                  key={category.id}
                >
                  {category.subCategories.length > 0 ? (
                    <AccordionTrigger
                      className={cn(
                        "px-4 py-4 flex items-center relative cursor-pointer outline-none hover:bg-gray-50 transition-colors duration-200 data-[state=open]:bg-primary/5 data-[state=open]:border-l-4 data-[state=open]:border-l-primary group"
                      )}
                    >
                      <Link
                        href={getCategoryPageUrl(category)}
                        onClick={closeMobileMenu}
                        className="w-fit text-left"
                      >
                        <Text
                          textStyle="TS4"
                          className="font-semibold text-gray-900 group-hover:text-primary transition-colors"
                        >
                          {category.name}
                        </Text>
                      </Link>
                    </AccordionTrigger>
                  ) : (
                    <Link
                      href={getCategoryPageUrl(category)}
                      className="w-full px-4 py-4 flex items-center justify-between hover:bg-gray-50 transition-colors duration-200 group"
                      onClick={closeMobileMenu}
                    >
                      <Text
                        textStyle="TS4"
                        className="font-semibold text-gray-900 group-hover:text-primary transition-colors"
                      >
                        {category.name}
                      </Text>
                      <ChevronRight
                        size={18}
                        className="shrink-0 transition-all duration-200 text-gray-400 group-hover:text-primary group-hover:translate-x-1"
                      />
                    </Link>
                  )}

                  {category.subCategories.length > 0 && (
                    <AccordionContent className="w-full bg-gray-50/50">
                      <ul className="px-2 pb-2 w-full space-y-1">
                        {category.subCategories.map((subCategory) =>
                          subCategory.subCategories.length > 0 ? (
                            <li
                              key={subCategory.id}
                              className="bg-white rounded-lg shadow-sm border border-gray-100"
                            >
                              <Accordion type="single" collapsible>
                                <AccordionItem
                                  className="border-0"
                                  value={subCategory.id}
                                >
                                  <AccordionTrigger className="px-4 py-3 flex items-center relative cursor-pointer outline-none hover:bg-gray-50 transition-colors duration-200 data-[state=open]:bg-primary/5 group">
                                    <Link
                                      href={getSubCategoryPageUrl(
                                        category,
                                        subCategory
                                      )}
                                      onClick={closeMobileMenu}
                                      className="w-fit text-left"
                                    >
                                      <Text
                                        textStyle="TS5"
                                        className="font-medium text-gray-800 group-hover:text-primary transition-colors"
                                      >
                                        {subCategory.name}
                                      </Text>
                                    </Link>
                                  </AccordionTrigger>

                                  {subCategory.subCategories.length > 0 && (
                                    <AccordionContent className="w-full bg-gray-50/30">
                                      <ul className="px-4 pb-3 w-full space-y-2">
                                        {subCategory.subCategories.map(
                                          (subSubCategory) => (
                                            <li
                                              key={subSubCategory.id}
                                              className="w-full"
                                            >
                                              <Link
                                                href={getSubSubCategoryPageUrl(
                                                  category,
                                                  subCategory,
                                                  subSubCategory
                                                )}
                                                className="w-full py-2 px-3 flex items-center justify-between hover:bg-white rounded-lg transition-colors duration-200 group"
                                                onClick={closeMobileMenu}
                                              >
                                                <Text
                                                  textStyle="TS6"
                                                  className="text-gray-700 group-hover:text-primary transition-colors"
                                                >
                                                  {subSubCategory.name}
                                                </Text>
                                                <ChevronRight
                                                  size={16}
                                                  className="shrink-0 transition-all duration-200 text-gray-400 group-hover:text-primary group-hover:translate-x-1"
                                                />
                                              </Link>
                                            </li>
                                          )
                                        )}
                                      </ul>
                                    </AccordionContent>
                                  )}
                                </AccordionItem>
                              </Accordion>
                            </li>
                          ) : (
                            <li key={subCategory.id} className="w-full">
                              <Link
                                href={getSubCategoryPageUrl(
                                  category,
                                  subCategory
                                )}
                                className="w-full py-3 px-4 flex items-center justify-between hover:bg-white rounded-lg transition-colors duration-200 group"
                                onClick={closeMobileMenu}
                              >
                                <Text
                                  textStyle="TS5"
                                  className="font-medium text-gray-800 group-hover:text-primary transition-colors"
                                >
                                  {subCategory.name}
                                </Text>
                                <ChevronRight
                                  size={16}
                                  className="shrink-0 transition-all duration-200 text-gray-400 group-hover:text-primary group-hover:translate-x-1"
                                />
                              </Link>
                            </li>
                          )
                        )}
                      </ul>
                    </AccordionContent>
                  )}
                </AccordionItem>
              ))
            ) : (
              <div className="p-8 text-center">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Grid3X3 className="w-8 h-8 text-gray-400" />
                </div>
                <Text textStyle="TS5" className="text-gray-500 font-medium">
                  {t("mobileMenu.categories")}
                </Text>
              </div>
            )}
          </Accordion>
        </div>
      </div>

      {/* Enhanced Footer */}
      <div className="p-4 bg-gradient-to-r from-slate-50 to-gray-50 border-t border-gray-100">
        <Link
          href={`tel:${companyPhoneNumber}`}
          target="_blank"
          rel="noopener noreferrer"
          className="w-full px-4 py-4 flex items-center justify-between bg-white rounded-xl shadow-sm border border-gray-100 hover:border-primary/30 hover:bg-primary/5 transition-all duration-200 group"
          onClick={closeMobileMenu}
        >
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-primary/10 rounded-lg group-hover:bg-primary/20 transition-colors">
              <ExternalLink className="w-4 h-4 text-primary" />
            </div>
            <Text
              textStyle="TS5"
              className="font-semibold text-gray-900 group-hover:text-primary transition-colors"
            >
              {t("mobileMenu.contact")}
            </Text>
          </div>
          <ChevronRight
            size={18}
            className="shrink-0 transition-all duration-200 text-gray-400 group-hover:text-primary group-hover:translate-x-1"
          />
        </Link>
      </div>
    </div>
  );
}
