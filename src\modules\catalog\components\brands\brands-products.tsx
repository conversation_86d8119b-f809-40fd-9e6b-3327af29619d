"use client";
import useBrands from "../../hooks/brands/use-brands";
import ProductsBrandSelector from "../products/overview/products-brand-selector";
import { ProductsOverviewSkeletons } from "../products/overview/default";

export default function BrandsProducts() {
  const { brands, brandsAreLoading } = useBrands({
    limit: 10,
  });

  return !brandsAreLoading ? (
    brands && brands.length > 0 ? (
      <ProductsBrandSelector brands={brands} />
    ) : null
  ) : (
    <ProductsOverviewSkeletons />
  );
}
