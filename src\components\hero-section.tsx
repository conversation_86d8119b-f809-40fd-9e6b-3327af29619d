"use client";
import { HTMLAttributes } from "react";
import { Carousel, CarouselContent, CarouselItem } from "./ui/carousel";
import useCarouselIndicator from "@/hooks/use-carousel-indicator";
import CarouselIndicator from "./carousel-indicator";
import { cn } from "@/lib/utils";
import Link from "next/link";
import useLandingPage from "@/hooks/use-landing-page";
import { Skeleton } from "./ui/skeleton";

export default function HeroSection(props: HTMLAttributes<"div">) {
  const { setApi, count, current } = useCarouselIndicator({
    autoChangement: true,
    timer: 30000,
  });

  const { landingPageContent, isLoading } = useLandingPage();

  return (
    <div className={cn("flex flex-col space-y-2", props.className)}>
      {!isLoading ? (
        landingPageContent &&
        (landingPageContent.images.length > 1 ? (
          <div className="flex flex-col space-y-4 items-center">
            <Carousel
              setApi={setApi}
              opts={{ loop: true }}
              className={cn(
                "w-full max-h-[600px] extraL:rounded-bl-[130px] L:rounded-bl-[80px] rounded-bl-none shadow-[0px_12px_45.9px_0px_rgba(0,0,0,0.25)] overflow-hidden"
              )}
            >
              <CarouselContent className="w-full flex">
                {landingPageContent.images.map((image, idx) => (
                  <CarouselItem key={idx} className="w-full">
                    <Link href={image.link ? image.link : "/"}>
                      <div className="relative  L:aspect-[1921/597] aspect-[450/600] ">
                        <picture>
                          {/* Mobile version — for screens below 600px */}
                          <source
                            srcSet={image.mobileImage}
                            media="(max-width: 599px)"
                          />

                          {/* Default image for L and above */}
                          <img
                            src={image.computerImage}
                            alt="hero section image"
                            className="absolute inset-0 w-full h-full object-cover object-right"
                          />
                        </picture>
                      </div>
                    </Link>
                  </CarouselItem>
                ))}
              </CarouselContent>
            </Carousel>

            <CarouselIndicator current={current} count={count} />
          </div>
        ) : (
          landingPageContent.images.length > 0 && (
            <Link
              href={
                landingPageContent.images[0].link
                  ? landingPageContent.images[0].link
                  : "/"
              }
            >
              <div className="relative max-h-[600px] L:aspect-[1921/597] aspect-[450/600] extraL:rounded-bl-[130px] L:rounded-bl-[80px] rounded-bl-none shadow-[0px_12px_45.9px_0px_rgba(0,0,0,0.25)] overflow-hidden">
                <picture>
                  {/* Mobile version — for screens below 600px */}
                  <source
                    srcSet={landingPageContent.images[0].mobileImage}
                    media="(max-width: 599px)"
                  />

                  {/* Default image for L and above */}
                  <img
                    src={landingPageContent.images[0].computerImage}
                    alt="hero section image"
                    className="absolute inset-0 w-full h-full object-cover object-right"
                  />
                </picture>
              </div>
            </Link>
          )
        ))
      ) : (
        <Skeleton className="w-full aspect-[1921/597] extraL:rounded-bl-[130px] L:rounded-bl-[80px] rounded-bl-none" />
      )}
    </div>
  );
}
