export default function CircularText({ text }: { text: string }) {
  const radius = 50;
  const textPathId = "text-path";

  const repeatedText = `${text} `.repeat(5);

  return (
    <svg width="120" height="120" viewBox="0 0 120 120" className="absolute">
      <defs>
        <path
          id={textPathId}
          d={`M 10,60 A ${radius},${radius} 0 1,1 110,60 A ${radius},${radius} 0 1,1 10,60`}
          fill="transparent"
        />
      </defs>
      <text fontSize="12" fill="black">
        <textPath href={`#${textPathId}`} startOffset="50%" textAnchor="middle">
          {repeatedText}
        </textPath>
      </text>
    </svg>
  );
}
