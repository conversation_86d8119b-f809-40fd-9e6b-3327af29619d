export interface UserSignUpType {
  email: string;
  firstName: string;
  lastName: string;
  password: string;
}

export interface UserSignInType {
  email: string;
  password: string;
}

export interface UserDataType {
  email: string;
  name: string;
  role: string;
  isAuthenticated: boolean;
  points: number;
}

export interface UserResponseDataType {
  email: string;
  name: string;
  points: string;
}
