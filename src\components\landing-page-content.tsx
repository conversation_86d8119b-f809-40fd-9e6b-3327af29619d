"use client";

import useLandingPage from "@/hooks/use-landing-page";
import { cn } from "@/lib/utils";
import { TextStyle } from "@/styles/text-styles";
import DOMPurify from "dompurify";
import { Skeleton } from "./ui/skeleton";

export default function LandingPageContent() {
  const { landingPageContent, isLoading } = useLandingPage();

  return (
    <div className="grid 3L:grid-cols-3 L:grid-cols-2 grid-cols-1 gap-3">
      {!(isLoading || landingPageContent === undefined) && landingPageContent
        ? landingPageContent.sections.map((section) => (
            <div
              key={section.id}
              className={cn(
                "text-gray prose prose-sm prose-a:underline prose-a:decoration-primary prose-a:text-primary",
                TextStyle["TS7"]
              )}
              dangerouslySetInnerHTML={{
                __html: DOMPurify.sanitize(section.description as string),
              }}
            ></div>
          ))
        : Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="w-full p-4 space-y-3">
              <Skeleton className="h-4 w-1/3" />
              {Array.from({ length: 8 }).map((_, i) => (
                <Skeleton key={i} className="h-4 w-full" />
              ))}
              <Skeleton className="h-3 w-5/6" />
              <Skeleton className="h-3 w-4/6" />
            </div>
          ))}
    </div>
  );
}
