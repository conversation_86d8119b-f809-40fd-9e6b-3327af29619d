"use client";
import { cn } from "@/lib/utils";
import Link from "next/link";
import type { ReactNode } from "react";

interface BannerProps {
  title?: string;
  symbol?: string;
  buttonText?: string;
  buttonUrl?: string;
  className?: string;
  backgroundImageUrl?: string;
  backgroundColor?: string;
  onClick?: () => void;
  children?: ReactNode;
}

export default function PromoBanner({
  title,
  symbol = "%",
  buttonText = "Voir Plus",
  buttonUrl = "#",
  className,
  backgroundImageUrl,
  backgroundColor = "#1EAAB4",
  onClick,
  children,
}: BannerProps) {
  return (
    <div
      className={cn(
        "w-full sm:w-[300px] rounded-xl overflow-hidden relative text-white ",
        className
      )}
      style={{ backgroundColor }}
    >
      {/* Background Pattern */}
      <div
        className=" bg-cover bg-center z-0"
        style={{ backgroundImage: `url(${backgroundImageUrl})` }}
      />

      {/* Content */}
      <div className="z-10 p-6 flex flex-col items-center justify-between h-full py-12">
        {/* Title */}
        <h2 className="text-3xl font-bold mb-2">{title}</h2>

        {/* Symbol */}
        <div className="text-8xl font-bold my-4 drop-shadow-[0_0_10px_rgba(255,255,255,0.7)]">
          {symbol}
        </div>

        {/* Custom Content */}
        {children}

        {/* Button */}
        {buttonText &&
          (buttonUrl ? (
            <Link
              href={buttonUrl}
              className="mt-4 bg-white text-[#1EAAB4] px-8 py-3 rounded-full font-bold hover:bg-opacity-90 transition-all inline-block"
            >
              {buttonText}
            </Link>
          ) : (
            <button
              onClick={onClick}
              className="mt-4 bg-white text-[#1EAAB4] font-bold  px-8 py-3 rounded-full hover:bg-opacity-90 transition-all"
            >
              {buttonText}
            </button>
          ))}
      </div>
    </div>
  );
}
