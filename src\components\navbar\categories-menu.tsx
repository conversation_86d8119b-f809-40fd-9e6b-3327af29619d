import { CategoryType } from "@/modules/catalog/types/categories";
import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import useScreenSize from "@/hooks/use-screen-size";
import { getCategoryPageUrl } from "@/modules/catalog/utils/urls";
import { screenWrapperStyle } from "../screen-wrapper";
import { Button } from "../ui/button";
import { useState } from "react";
import NavbarCategoriesDropdown from "./navbar-categories-dropdown";
import Link from "next/link";
import { ChevronDown } from "lucide-react";
import { useTranslations } from "next-intl";

interface Props {
  categories: CategoryType[];
}

export default function CategoriesMenu({ categories }: Props) {
  const { width } = useScreenSize();
  const t = useTranslations("shared.navbar");
  const [hoveredCategory, setHoveredCategory] = useState<CategoryType | null>(
    null
  );
  const [hoveredSubCategory, setHoveredSubCategory] =
    useState<CategoryType | null>(null);
  const [dropDownIsOpen, setDropDownIsOpen] = useState(false);

  const handleCategoryHovering = (category: CategoryType) => {
    setHoveredCategory(category);
    setHoveredSubCategory(
      category.subCategories.length > 0 ? category.subCategories[0] : null
    );
    setDropDownIsOpen(true);
  };

  const handleSubCategoryHovering = (subCategory: CategoryType) => {
    setHoveredSubCategory(subCategory);
  };

  if (width < 900 || !categories || categories.length === 0) {
    return null;
  }

  return (
    <div className=" bg-white text-blue flex 2extraL:justify-center border-y border-y-gray/20">
      <nav
        aria-label="categories navigation"
        className={cn(
          "relative w-full flex 2extraL:flex-row 2extraL:items-center flex-col  justify-center gap-y-1 2extraL:space-x-10 ",
          screenWrapperStyle,
          "py-0"
        )}
      >
        <Link
          href={"/produits/filtres"}
          className={cn(
            "w-fit text-blue before:bg-primary h-full items-center px-3 py-1 hover:before:w-0 hover:bg-gradient-to-t hover:from-blue/15 hover:to-transparent rounded-none flex space-x-2",
            {}
          )}
        >
          <Text textStyle="TS7">{t("allProducts")}</Text>
        </Link>
        {categories.map((category) => (
          <div key={category.id} className="px-0 2extraL:block flex flex-col">
            <Button
              variant="link"
              onClick={() => {
                setDropDownIsOpen(false);
              }}
              onMouseEnter={() => handleCategoryHovering(category)}
              onMouseLeave={() => setDropDownIsOpen(false)}
              className={cn(
                "w-fit text-blue before:bg-primary px-3 py-1 hover:before:w-0 hover:bg-gradient-to-t hover:from-blue/15 hover:to-transparent rounded-none flex space-x-2",
                {}
              )}
            >
              <Link href={getCategoryPageUrl(category)}>
                <Text textStyle="TS7">{category.name}</Text>
              </Link>

              {category.subCategories.length > 0 && (
                <ChevronDown size={16} className={cn("text-blue text-sm")} />
              )}
            </Button>
          </div>
        ))}
        <NavbarCategoriesDropdown
          setDropDownIsOpen={setDropDownIsOpen}
          dropDownIsOpen={dropDownIsOpen}
          hoveredCategory={hoveredCategory}
          handleSubCategoryHovering={handleSubCategoryHovering}
          hoveredSubCategory={hoveredSubCategory}
        />
      </nav>
    </div>
  );
}
