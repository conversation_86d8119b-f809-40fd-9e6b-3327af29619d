import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";
import { TextStyle } from "@/styles/text-styles";

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 active:scale-95 whitespace-nowrap rounded-md text-sm font-medium focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-neutral-950 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 dark:focus-visible:ring-neutral-300 duration-300",
  {
    variants: {
      variant: {
        default:
          "rounded-full bg-blue text-white shadow hover:bg-white hover:text-primary hover:border border-primary",
        destructive: "bg-red-500 text-neutral-50 shadow-sm",
        outline:
          "border border-neutral-200 bg-white shadow-sm hover:bg-neutral-100 hover:text-neutral-900 dark:border-neutral-800 dark:bg-neutral-950 dark:hover:bg-neutral-800 dark:hover:text-neutral-50",
        secondary:
          "rounded-full bg-white text-secondary border border-secondary",
        voirplus:
          "rounded-full bg-white text-blue border border-blue shadow-sm hover:bg-white hover:text-blue hover:border-blue transition-all duration-300 transform hover:scale-105",
        ghost: "",
        link: "relative hover:before:w-full before:w-0 before:absolute before:content-[''] before:left-0 before:bottom-1 before:h-[1px] before:bg-black ",
      },
      size: {
        default: "h-10 px-4 py-2",
        tiny: "h-fit w-fit",
        sm: "h-[50px] px-10 py-3 rounded-full",
        lg: "rounded-full py-4 px-6",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  (
    { className, variant = "default", size, asChild = false, ...props },
    ref
  ) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }), {
          [TextStyle["TS8"]]: variant === "default",
          [TextStyle["TS6"]]: variant === "secondary",
        })}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = "Button";

export { Button, buttonVariants };
