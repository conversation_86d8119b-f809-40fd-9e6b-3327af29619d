import { cn } from "@/lib/utils";
import { InputHTMLAttributes, useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { InputWarning } from "./input-warning";
import EyeIcon from "@assets/icons/eye-icon";

interface WarnInputPropsType extends InputHTMLAttributes<HTMLInputElement> {
  warning: string;
  eyeColor?: string;
  parentclassname?: string;
}
export default function WarnInput({
  warning,
  className = "",
  type,
  id,
  eyeColor = "gray",
  value,
  defaultValue,
  ...props
}: WarnInputPropsType) {
  const [passwordType, setPasswordType] = useState("password");
  const [inputValue, setInputValue] = useState(value || defaultValue || "");

  useEffect(() => {
    if (value !== undefined) {
      setInputValue(value);
    }
  }, [value]);

  const hasValue = inputValue && inputValue.toString().length > 0;

  return (
    <div className={cn("relative flex items-center", props.parentclassname)}>
      <Input
        id={id}
        className={cn(
          "rounded-md h-11 font-tajawal transition-all duration-200",
          {
            "border-[1px] border-danger": warning && warning !== "",
            "bg-white border-primary": !hasValue && !warning,
            "bg-white border-primary border": hasValue && !warning,
          },
          className
        )}
        type={type === "password" ? passwordType : type}
        value={inputValue}
        onChange={(e) => {
          setInputValue(e.target.value);
          if (props.onChange) {
            props.onChange(e);
          }
        }}
        {...props}
      />

      <div
        className={cn(
          "absolute right-3 flex items-center space-x-2 text-primary"
        )}
      >
        {["password", "text"].includes(type as string) ? (
          <div
            className="cursor-pointer h-4 w-5 flex items-center justify-center active:opacity-70"
            onClick={() =>
              setPasswordType(passwordType === "password" ? "text" : "password")
            }
          >
            <EyeIcon />
          </div>
        ) : null}
        <InputWarning warning={warning} />
      </div>
    </div>
  );
}
