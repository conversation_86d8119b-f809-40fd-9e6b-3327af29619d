@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .hide-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .hide-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}

body {
  font-family: var(--font-dm-sans), "DM Sans", Arial, Helvetica, sans-serif;
  overflow-x: hidden;
  max-width: 100vw;
}

html {
  overflow-x: hidden;
  max-width: 100vw;
}

* {
  box-sizing: border-box;
}

@layer base {
  :root {
    --radius: 0.5rem;
  }
}

/* Simple mobile menu styles */

/* Mobile menu styles */
.menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.menu-overlay.active {
  opacity: 1;
  visibility: visible;
}

.mobile-menu {
  position: fixed;
  top: 0;
  left: -100%;
  width: 320px;
  max-width: 85vw;
  height: 100vh;
  background: white;
  z-index: 1000;
  transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow-y: auto;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
}

.mobile-menu.active {
  left: 0;
}

.mobile-menu-close {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #333;
  padding: 4px;
  border-radius: 50%;
  transition: background-color 0.2s ease;
}

.mobile-menu-close:hover {
  background-color: #f3f4f6;
  @apply bg-primary;
}
.menu-btn-on.menu-btn:after {
  transform: rotate(-45deg);
  top: 0;
  @apply bg-primary;
}

/* Mobile bottom navbar */
.mobile-bottom-navbar {
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  width: 100%;
  max-width: 100vw;
  overflow: hidden;
}

/* Add bottom padding to body when mobile navbar is present */
@media (max-width: 767px) {
  body {
    padding-bottom: 55px;
    overflow-x: hidden;
    max-width: 100vw;
  }

  /* Ensure mobile navbar doesn't overflow */
  .mobile-bottom-navbar {
    left: 0;
    right: 0;
    margin: 0;
    padding: 0;
  }

  /* Prevent any element from causing horizontal overflow */
  * {
    max-width: 100vw;
    box-sizing: border-box;
  }

  /* Ensure header doesn't overflow */

  /* Mobile header spacing */
  .mobile-header-container {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  /* Mobile horizontal scroll styling */
  .overflow-x-auto {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }

  .overflow-x-auto::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  /* Smooth scrolling for mobile category filters */
  .overflow-x-auto {
    scroll-behavior: smooth;
  }

  /* Optimize button spacing on very small screens */
  @media (max-width: 360px) {
    .mobile-bottom-navbar > div {
      padding-left: 0.25rem;
      padding-right: 0.25rem;
    }

    .mobile-bottom-navbar button {
      padding: 0.125rem;
    }
  }

  /* Ensure proper width constraints */
  .mobile-bottom-navbar * {
    box-sizing: border-box;
  }
}

.clipPath-polygon {
  clippath: polygon(0 0, 100% 50%, 100% 100%, 0% 100%);
}

@media (min-width: 600px) {
  .clipPath-polygon {
    clippath: polygon(0 0, 55% 0, 79% 100%, 0% 100%);
  }
}

/*
scroll bar styling*/
/* Apply to all scrollbars */
::-webkit-scrollbar {
  width: 8px; /* 'scrollbar-thin' reduces the width */
  height: 8px; /* For horizontal scrollbars */
}

::-webkit-scrollbar-track {
  background: transparent; /* 'scrollbar-track-transparent' */
}

::-webkit-scrollbar-thumb {
  @apply bg-primary;
  border-radius: 0px;
}

/* Optional: Change color on hover */
::-webkit-scrollbar-thumb:hover {
  @apply bg-primary/90;
}

@keyframes revealImage {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  50% {
    transform: translateX(50%);
    opacity: 0.5;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Custom animation class */
.hero-reveal-animation {
  @apply bg-primary;
  animation: revealImage 3.5s cubic-bezier(0.4, 0, 0.2, 1) forwards;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}
