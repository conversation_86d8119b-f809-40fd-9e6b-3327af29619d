import { type Dispatch, type SetStateAction, useRef } from "react";
import {
  getCategoryPageUrl,
  getSubCategoryPageUrl,
  getSubSubCategoryPageUrl,
} from "@/modules/catalog/utils/urls";
import { cn } from "@/lib/utils";
import type { CategoryType } from "@/modules/catalog/types/categories";
import Text from "@/styles/text-styles";
import { Button } from "../ui/button";
import Link from "next/link";
import { ChevronRight } from "lucide-react";
import { useTranslations } from "next-intl";
import Image from "next/image";

interface Props {
  setDropDownIsOpen: Dispatch<SetStateAction<boolean>>;
  dropDownIsOpen: boolean;
  hoveredCategory: CategoryType | null;
  hoveredSubCategory: CategoryType | null;
  handleSubCategoryHovering: (s: CategoryType) => void;
}

export default function NavbarCategoriesDropdown({
  setDropDownIsOpen,
  dropDownIsOpen,
  hoveredCategory,
  handleSubCategoryHovering,
}: Props) {
  const t = useTranslations("shared.navbar.categories");
  const dropdownRef = useRef<HTMLDivElement>(null);

  return (
    <div
      ref={dropdownRef}
      onMouseEnter={() => setDropDownIsOpen(true)}
      onMouseLeave={() => setDropDownIsOpen(false)}
      className={cn(
        "absolute before:z-50 before:absolute before:content-[''] before:left-0 before:right-0 before:bottom-0 before:h-2 before:bg-blue shadow-md left-0 right-0 top-[100%] overflow-hidden duration-500 z-50",
        {
          "max-h-0": !dropDownIsOpen,
          "max-h-[80vh]": dropDownIsOpen,
        }
      )}
    >
      {hoveredCategory && hoveredCategory.subCategories.length > 0 && (
        <div className="w-full bg-white h-[70vh] border-t border-gray-light overflow-y-auto">
          {/* Category Header */}
          <div className="container mx-auto px-4 md:px-6 lg:px-8 mt-8">
            <Text textStyle="TS4" className="font-bold text-primary">
              {hoveredCategory.name}
            </Text>
          </div>

          <div className="container mx-auto pt-4 pb-8 px-4 md:px-6 lg:px-8">
            {/* Grid layout for categories with images */}
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-6 mb-6">
              {hoveredCategory.subCategories.slice(0, 12).map((subCategory) => (
                <div
                  key={subCategory.id}
                  className="flex flex-col items-center group cursor-pointer relative"
                  onMouseEnter={() => handleSubCategoryHovering(subCategory)}
                >
                  <Link
                    onClick={() => {
                      setDropDownIsOpen(false);
                    }}
                    href={getSubCategoryPageUrl(hoveredCategory, subCategory)}
                    className="flex flex-col items-center w-full"
                  >
                    {/* Category Image Container */}
                    <div className="relative w-full h-[140px] mb-3 bg-gray-50 rounded-lg overflow-hidden group-hover:shadow-md transition-all duration-300">
                      <div className="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                        <Image
                          unoptimized
                          width={120}
                          height={120}
                          alt={`${subCategory.name}`}
                          src={
                            subCategory.image || "/not-found/product-image.webp"
                          }
                        />
                      </div>

                      {/* Hover overlay */}
                      <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300"></div>
                    </div>

                    {/* Category Name with Arrow */}
                    <div className="flex items-center justify-center w-full text-center">
                      <Text
                        textStyle="TS6"
                        className="text-black font-bold group-hover:text-primary transition-colors duration-300 truncate max-w-full"
                      >
                        {subCategory.name}
                      </Text>
                      <ChevronRight
                        size={20}
                        className=" text-black group-hover:text-primary group-hover:translate-x-0.5 transition-all duration-300 flex-shrink-0"
                      />
                    </div>
                  </Link>

                  {/* Sub-subcategories dropdown on hover */}
                  {subCategory.subCategories.length > 0 && (
                    <div className="absolute top-0 left-full w-64 bg-white shadow-xl border border-gray-200 rounded-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 z-20 ml-2">
                      <div className="p-4">
                        <div className="flex items-center mb-3 pb-2 border-b border-gray-100">
                          <Text
                            textStyle="TS6"
                            className="font-bold text-gray-dark"
                          >
                            {subCategory.name}
                          </Text>
                          <ChevronRight
                            size={12}
                            className="ml-1 text-gray-400"
                          />
                        </div>
                        <div className="grid grid-cols-1 gap-1 max-h-64 overflow-y-auto">
                          {subCategory.subCategories
                            .slice(0, 8)
                            .map((subSubCategory) => (
                              <Link
                                key={subSubCategory.id}
                                onClick={() => {
                                  setDropDownIsOpen(false);
                                }}
                                href={getSubSubCategoryPageUrl(
                                  hoveredCategory,
                                  subCategory,
                                  subSubCategory
                                )}
                                className="flex items-center justify-between py-2 px-3 text-sm text-gray-600 hover:text-primary hover:bg-gray-50 rounded-md transition-all duration-200 group/item"
                              >
                                <Text
                                  textStyle="TS7"
                                  className="truncate flex-1"
                                >
                                  {subSubCategory.name}
                                </Text>
                                <ChevronRight
                                  size={12}
                                  className="ml-2 text-gray-400 group-hover/item:text-primary group-hover/item:translate-x-0.5 transition-all duration-200 flex-shrink-0"
                                />
                              </Link>
                            ))}
                          {subCategory.subCategories.length > 8 && (
                            <Link
                              onClick={() => {
                                setDropDownIsOpen(false);
                              }}
                              href={getSubCategoryPageUrl(
                                hoveredCategory,
                                subCategory
                              )}
                              className="flex items-center justify-center py-2 px-3 text-sm text-primary hover:bg-primary/5 rounded-md transition-colors duration-200 font-medium mt-2 border-t border-gray-100"
                            >
                              <Text textStyle="TS7">
                                Voir tous ({subCategory.subCategories.length})
                              </Text>
                              <ChevronRight size={12} className="ml-1" />
                            </Link>
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Second row if there are more categories */}
            {hoveredCategory.subCategories.length > 12 && (
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-6 mb-6 pt-4 border-t border-gray-100">
                {hoveredCategory.subCategories
                  .slice(12, 24)
                  .map((subCategory) => (
                    <div
                      key={subCategory.id}
                      className="flex flex-col items-center group cursor-pointer"
                      onMouseEnter={() =>
                        handleSubCategoryHovering(subCategory)
                      }
                    >
                      <Link
                        onClick={() => {
                          setDropDownIsOpen(false);
                        }}
                        href={getSubCategoryPageUrl(
                          hoveredCategory,
                          subCategory
                        )}
                        className="flex flex-col items-center w-full"
                      >
                        {/* Category Image Container */}
                        <div className="relative w-full aspect-square mb-3 bg-gray-50 rounded-lg overflow-hidden group-hover:shadow-md transition-all duration-300">
                          <div className="absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center">
                            <Image
                              unoptimized
                              width={120}
                              height={120}
                              alt={`${subCategory.name}`}
                              src={
                                subCategory.image ||
                                "/not-found/product-image.webp" ||
                                "/placeholder.svg"
                              }
                            />
                          </div>

                          {/* Hover overlay */}
                          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300"></div>
                        </div>

                        {/* Category Name with Arrow */}
                        <div className="flex items-center justify-center w-full text-center">
                          <Text
                            textStyle="TS6"
                            className="text-gray-dark font-medium group-hover:text-primary transition-colors duration-300 truncate max-w-full"
                          >
                            {subCategory.name}
                          </Text>
                          <ChevronRight
                            size={14}
                            className="ml-1 text-gray-400 group-hover:text-primary group-hover:translate-x-0.5 transition-all duration-300 flex-shrink-0"
                          />
                        </div>
                      </Link>
                    </div>
                  ))}
              </div>
            )}

            {/* See More button */}
            {hoveredCategory.subCategories.length > 24 && (
              <div className="w-full flex justify-center mt-6 pt-4 border-t border-gray-100">
                <Button
                  variant="link"
                  className="text-primary hover:text-primary/80 flex items-center gap-1 group"
                  onClick={() => {
                    setDropDownIsOpen(false);
                  }}
                >
                  <Link
                    href={getCategoryPageUrl(hoveredCategory)}
                    className="flex items-center"
                  >
                    <Text textStyle="TS5" className="font-medium">
                      {t("seeMoreCategories")}
                    </Text>
                    <ChevronRight
                      size={16}
                      className="ml-1 group-hover:translate-x-1 transition-transform"
                    />
                  </Link>
                </Button>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
