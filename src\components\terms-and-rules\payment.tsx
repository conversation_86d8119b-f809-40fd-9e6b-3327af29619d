import BreadCrumb from "@/components/breadcrumb";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";

export const Payment = () => {
  const t = useTranslations("payment");

  return (
    <div>
      <BreadCrumb />

      <div className="text-3xl font-bold text-primary mt-4 mb-5">
        <Text textStyle="TS2">{t("title")}</Text>
      </div>

      <div className="px-4 L:px-8 py-5 space-y-4">
        <div>
          <Text textStyle="TS5">
            {t.rich("paragraph", {
              br: () => <br />,
              li: (chunk) => <li>{chunk}</li>,
              ul: (chunk) => <ul className="list-disc pl-4">{chunk}</ul>,
            })}
          </Text>
        </div>
      </div>
    </div>
  );
};
