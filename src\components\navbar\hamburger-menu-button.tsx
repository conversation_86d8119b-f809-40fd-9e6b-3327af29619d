"use client";
import { Menu } from "lucide-react";
import { useState, useEffect } from "react";
import { useTranslations } from "next-intl";

export default function HamburgerMenuButton() {
  const t = useTranslations("shared.navbar.mobileMenu");
  const [isActive, setIsActive] = useState(false);

  useEffect(() => {
    const menu = document.querySelector(".mobile-menu");

    const observer = new MutationObserver(() => {
      if (menu) {
        const isOpen = menu.classList.contains("active");
        setIsActive(isOpen);
      }
    });

    if (menu) {
      observer.observe(menu, { attributes: true, attributeFilter: ["class"] });
    }

    return () => observer.disconnect();
  }, []);

  const handleClick = () => {
    const menu = document.querySelector(".mobile-menu");
    const overlay = document.querySelector(".menu-overlay");

    if (menu && overlay) {
      const isOpen = menu.classList.contains("active");

      if (isOpen) {
        // Close menu
        menu.classList.remove("active");
        overlay.classList.remove("active");
        document.body.style.overflow = "";
      } else {
        // Open menu
        menu.classList.add("active");
        overlay.classList.add("active");
        document.body.style.overflow = "hidden";
      }
    }
  };

  return (
    <button
      onClick={handleClick}
      aria-label={t("menu")}
      className={`w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200 ${
        isActive
          ? "bg-primary text-white"
          : "bg-gray-100 text-gray-600 hover:bg-gray-200"
      }`}
    >
      <Menu size={18} />
    </button>
  );
}
