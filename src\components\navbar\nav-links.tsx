"use client";
import { useTranslations } from "next-intl";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";

interface NavLinkProps {
  href: string;
  label: string;
  hasDropdown?: boolean;
  isActive?: boolean;
}

const NavLink = ({ href, label, isActive = false }: NavLinkProps) => {
  return (
    <Link
      href={href}
      className={cn(
        "relative px-6 py-3 text-lg font-medium hover:text-primary transition-colors",
        isActive ? "text-primary" : "text-black"
      )}
    >
      <div className="flex items-center justify-center">{label}</div>
      {isActive && (
        <span className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary"></span>
      )}
    </Link>
  );
};

export default function NavLinks() {
  const t = useTranslations("shared.navbar");
  const pathname = usePathname();

  const navItems = [
    { key: "home", href: "/", label: t("home") },
    {
      key: "shop",
      href: "/produits/filtres",
      label: t("shop"),
      hasDropdown: true,
    },
    {
      key: "contact",
      href: "https://www.linkedin.com/company/tawer-digital-group/posts/?feedView=all",
      label: t("contact"),
    },
  ];

  return (
    <nav className="hidden md:flex items-center space-x-2  w-full justify-center">
      {navItems.map((item) => (
        <NavLink
          key={item.key}
          href={item.href}
          label={item.label}
          hasDropdown={item.hasDropdown}
          isActive={
            pathname === item.href || pathname.startsWith(`${item.href}/`)
          }
        />
      ))}
    </nav>
  );
}
