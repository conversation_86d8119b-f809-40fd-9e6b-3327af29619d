import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { HTMLAttributes, useState } from "react";
import Text from "@/styles/text-styles";
import { cn } from "@/lib/utils";
import { CircleX } from "lucide-react";

interface InputWarningPropsType extends HTMLAttributes<HTMLDivElement> {
  warning: string;
}

export function InputWarning({ warning, className }: InputWarningPropsType) {
  const [isOpen, setIsOpen] = useState(false);
  return warning && warning !== "" ? (
    <HoverCard open={isOpen}>
      <HoverCardTrigger
        asChild
        className={className}
        onMouseEnter={() => setIsOpen(true)}
        onMouseLeave={() => setIsOpen(false)}
      >
        <CircleX className="text-danger" />
      </HoverCardTrigger>
      <HoverCardContent
        className={cn("px-2 w-fit")}
        color="#C48840"
        onMouseEnter={() => setIsOpen(true)}
        onMouseLeave={() => setIsOpen(false)}
      >
        <Text textStyle="TS7" className="text-danger">
          {warning}
        </Text>
      </HoverCardContent>
    </HoverCard>
  ) : null;
}
