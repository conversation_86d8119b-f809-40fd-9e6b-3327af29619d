import { GET } from "@/lib/http-methods";
import { BrandInResponseType } from "../../types/brands";
import { castToBrandType } from "../../utils/types-casting/brands";
import { PaginationType } from "@/types";

interface Params {
  categoriesSlugs?: string[];
  productPriceRange?: number[]; // [min, max]
  searchByProductName?: string;
  productInStock?: boolean;
  brandsSlugs?: string[];
  productSlugs?: string[];
  search?: string;
  limit?: number;
  page?: number;
}

export async function retrieveBrandsFromServerSide({
  categoriesSlugs,
  productPriceRange,
  searchByProductName,
  productInStock,
  brandsSlugs,
  productSlugs,
  search,
  limit,
  page,
}: Params) {
  try {
    const params = [];

    if (page) params.push(`page=${page}`);
    if (limit) params.push(`limit=${limit}`);
    if (brandsSlugs?.length) params.push(`brandSlugs=${brandsSlugs.join(",")}`);
    if (productSlugs?.length)
      params.push(`productSlugs=${productSlugs.join(",")}`);
    if (categoriesSlugs?.length)
      params.push(`categorySlugs=${categoriesSlugs.join(",")}`);
    if (productPriceRange?.[0] != null)
      params.push(`productMinPrice=${productPriceRange[0]}`);
    if (productPriceRange?.[1] != null)
      params.push(`productMaxPrice=${productPriceRange[1]}`);
    if (typeof productInStock === "boolean")
      params.push(`productInStock=${productInStock}`);
    if (searchByProductName)
      params.push(
        `searchByProductName=${encodeURIComponent(searchByProductName)}`
      );
    if (search) params.push(`search=${encodeURIComponent(search)}`);

    const query = params.join("&");
    const res = await GET(`/brands?${query}`, {});

    return {
      pagination: res.data.pagination as PaginationType,
      brands: (res.data.data as BrandInResponseType[]).map((brandInResponse) =>
        castToBrandType(brandInResponse)
      ),
    };
  } catch (error) {
    return null;
  }
}
