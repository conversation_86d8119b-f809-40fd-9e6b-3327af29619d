import { useEffect } from "react";
import { useRouter } from "next/navigation";
import useUserStore from "../store/user-store";

interface Params {
  delay: number; //delay in ms
}

export default function useDelaySignUpPopup({ delay }: Params) {
  const router = useRouter();
  const { user } = useUserStore((store) => store);

  useEffect(() => {
    if (localStorage.getItem("signUpPopup") !== "displayed") {
      //sign up pop up appear only once for the user
      setTimeout(() => {
        if (!user || (user && !user.isAuthenticated)) {
          //we make sure that user is not authenticated
          router.push("/auth/sign-up");
          localStorage.setItem("signUpPopup", "displayed");
        }
      }, delay);
    }
  }, [delay, router, user]);
}
