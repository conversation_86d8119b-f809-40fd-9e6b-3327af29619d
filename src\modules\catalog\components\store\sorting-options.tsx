import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useTranslations } from "next-intl";
import { SortingCriteria } from "./filter-options/sorting-criteria-dropdown";
import { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { useProductsFilteringStore } from "../../store/products-filter";
import { CriteriaType } from "../../types";

export default function SortingOptions() {
  const t = useTranslations("filtersPage");
  const { criteria, setCriteria } = useProductsFilteringStore();
  const searchParams = useSearchParams();
  const sorting = searchParams.get("sort");
  const criteriaInParam = searchParams.get("criteria") as CriteriaType;

  const handleCriteriaChangement = (criteria: CriteriaType) => {
    setCriteria(criteria);
  };

  //used to check how to sort products
  useEffect(() => {
    if (sorting && sorting === "newer") {
      setCriteria("createdAtDesc");
    } else if (criteriaInParam) {
      setCriteria(criteriaInParam);
    }

    return () => setCriteria("priceAsc");
  }, [sorting]);

  return (
    <div className="flex gap-2 whitespace-nowrap justify-end mt-6 w-1/2 ms-auto extraTinyL:mt-0 tinyL:w-auto">
      <label className="font-bold text-primary">
        {`${t.raw("sortDropdownPlaceholder")}:`}
      </label>

      <Select
        value={criteria}
        onValueChange={(value: CriteriaType) => handleCriteriaChangement(value)}
      >
        <SelectTrigger className="relative h-fit py-0 px-0 flex space-x-3 text-primary border-transparent shadow-none text-sm focus:ring-transparent w-fit before:content-[''] before:absolute before:left-0 before:w-0 before:h-[1px] before:bg-primary before:bottom-0 group-hover:before:w-full before:duration-300">
          <SelectValue placeholder={t("sortingCriteria.priceLowToHigh")} />
        </SelectTrigger>
        <SelectContent>
          {SortingCriteria.map((option, index) => (
            <SelectItem key={index} value={option.value}>
              {t("sortingCriteria." + option.label)}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
