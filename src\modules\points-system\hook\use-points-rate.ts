import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { retrievePointsRateFromServerSide } from "../service/points-rate-extraction";
import { PointsRateType } from "../types";

export default function usePointsRate() {
  const { data, isLoading, isError } = useQuery<PointsRateType>({
    queryKey: ["points-rate"],
    queryFn: retrievePointsRateFromServerSide,
    placeholderData: keepPreviousData,
  });

  return {
    pointsRate: data ? data.rate : 0.005,
    isLoading,
  };
}
