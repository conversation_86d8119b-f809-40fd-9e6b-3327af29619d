import Text from "@/styles/text-styles";
import { ProductType } from "../../../../types/products";
import Image from "next/image";
import Link from "next/link";
import useProductUrl from "../../../../hooks/products/use-product-url";
import { Skeleton } from "@/components/ui/skeleton";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import HeartButton from "@/components/ui/heart-button";
import { useWishlistStore } from "@/modules/wishlist/store/wishlist-store";
import { useCartStore } from "@/modules/cart/store/cart-store";
import useCurrency from "@/modules/catalog/hooks/use-currency";
import { formatPrice } from "../../../../utils/prices-transformation";

interface Props {
  product: ProductType | null;
  onClick?: () => void;
}

export default function SearchProductContainer({ product, onClick }: Props) {
  const t = useTranslations("catalog.product");
  const productPageUrl = useProductUrl(product);
  const [productImage, setProductImage] = useState(
    "/not-found/product-image.webp"
  );

  const {
    actions: {
      toggleProductItem,
      isProductInWishlist: checkIsProductInWishlist,
    },
  } = useWishlistStore();
  const {
    actions: { addProductItem: addProductItemToCart },
  } = useCartStore();
  const { currency } = useCurrency();

  const availablePromotion =
    product &&
    product.items[0].prices[0].promotionalPrice !==
      product.items[0].prices[0].realPrice;

  const isProductInWishlist = product
    ? checkIsProductInWishlist(product.items[0].id)
    : false;

  useEffect(() => {
    if (product && product.items.length > 0)
      setProductImage(product.items[0].image);
  }, [product]);

  const handleQuickAdd = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (product) {
      addProductItemToCart({
        slug: product.slug,
        id: product.items[0].id,
        productId: product.id,
        cartQuantity: 1,
        name: product.name,
        prices: product.items[0].prices,
        image: product.items[0].image,
        variations: product.items[0].variations,
        inStock: product.items[0].inStock,
      });
    }
  };

  const handleWishlistToggle = () => {
    if (product) {
      toggleProductItem({
        slug: product.slug,
        productId: product.id,
        id: `temp-${product.items[0].id}`,
        productItemId: product.items[0].id,
        name: product.name,
        image: product.items[0].image,
        prices: product.items[0].prices,
        variations: product.items[0].variations,
        inStock: product.items[0].inStock,
      });
    }
  };

  return product ? (
    <div className="w-full flex flex-col rounded-md shadow-sm overflow-hidden group h-full hover:shadow-md transition-shadow">
      <Link
        href={productPageUrl}
        onClick={onClick}
        className="w-full flex flex-col h-full"
      >
        {/* Product Image with Heart Icon */}
        <div className="relative w-full pt-[100%] bg-white">
          <Image
            className="object-cover absolute inset-0 w-full h-full"
            fill
            alt={product.name}
            unoptimized
            onError={() => setProductImage("/not-found/product-image.webp")}
            src={productImage}
          />
          <HeartButton
            size="sm"
            isLiked={isProductInWishlist}
            className="absolute top-2 right-2 S:top-3 S:right-3"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleWishlistToggle();
            }}
          />
        </div>

        {/* Product Info */}
        <div className="flex flex-col p-2 S:p-3 M:p-4 flex-grow">
          {/* Vendor - Static content, not served from backend */}
          {/* <div className="flex items-center mb-2">
            <Text
              textStyle="TS7"
              className="text-gray-500 flex items-center text-xs"
            >
              <span className="text-gray-600">{t("vendor")}</span>
              <span className="mx-1 text-gray-400">•</span>
              <span className="flex items-center">
                <span className="text-yellow-500 mr-1">★</span>
                <span className="text-gray-600">0.00</span>
              </span>
              <span className="text-gray-400 ml-1">{t("noReviews")}</span>
            </Text>
          </div> */}

          {/* Product Name */}
          <Text
            textStyle="TS7"
            className="font-medium text-gray-800 line-clamp-2 text-xs S:text-sm mb-2 S:mb-3 leading-tight"
          >
            {product.name}
          </Text>

          {/* Price - Pushed to bottom */}
          <div className="flex items-center mt-auto">
            <Text
              textStyle="TS6"
              className="font-semibold text-gray-900 text-sm S:text-base"
            >
              {`${formatPrice(
                product.items[0]?.prices[0]?.promotionalPrice
              )} ${currency}`}
            </Text>
            {availablePromotion && (
              <Text
                textStyle="TS7"
                className="line-through text-gray-500 ml-1 S:ml-2 text-xs S:text-sm"
              >
                {`${formatPrice(
                  product.items[0].prices[0].realPrice
                )} ${currency}`}
              </Text>
            )}
          </div>
        </div>
      </Link>

      <div className="px-2 S:px-3 M:px-4 pb-2 S:pb-3 M:pb-4 pt-1">
        <Button
          variant="outline"
          className="w-full rounded-md border-gray-300 text-gray-700 hover:bg-gray-100 text-xs S:text-sm py-1.5 S:py-2 h-auto font-normal"
          onClick={handleQuickAdd}
        >
          {t("quickAdd")}
        </Button>
      </div>
    </div>
  ) : (
    <div className="w-full flex flex-col rounded-md shadow-sm border border-gray-200 overflow-hidden h-full">
      {/* Skeleton for image */}
      <div className="relative w-full pt-[100%] bg-gray-50">
        <Skeleton className="absolute inset-0 w-full h-full" />
        <div className="absolute top-3 right-3 w-8 h-8 rounded-full">
          <Skeleton className="w-full h-full rounded-full" />
        </div>
      </div>

      {/* Skeleton for content */}
      <div className="p-4 flex-grow flex flex-col">
        {/* Vendor info skeleton */}
        <div className="flex items-center mb-3">
          <Skeleton className="h-3 w-[60%]" />
          <div className="mx-1"></div>
          <Skeleton className="h-3 w-[30%]" />
        </div>

        {/* Product name skeleton */}
        <Skeleton className="h-4 w-[95%] mb-2" />
        <Skeleton className="h-4 w-[80%] mb-4" />

        {/* Price skeleton - Pushed to bottom */}
        <div className="flex items-center mt-auto">
          <Skeleton className="h-5 w-[30%]" />
        </div>
      </div>

      {/* Quick add button skeleton */}
      <div className="px-4 pb-4 pt-1">
        <Skeleton className="h-9 w-full rounded-md" />
      </div>
    </div>
  );
}
