import Store from "@/modules/catalog/components/store";
import { retrieveCategoryFromServerSide } from "@/modules/catalog/services/categories/category-extraction";
import { retrieveProductsFromServerSide } from "@/modules/catalog/services/products/products-extraction";
import { generateCategoryMetadata } from "@/modules/catalog/utils/seo/meta-data/categories";
import { getProductPageUrl } from "@/modules/catalog/utils/urls";
import { Metadata } from "next";
import { notFound } from "next/navigation";

interface Props {
  params: Promise<{
    categories: string[];
  }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { categories } = await params;
  const categoryMetaData = await generateCategoryMetadata({
    categorySlug: categories[categories.length - 1],
  });

  categoryMetaData.metadataBase = new URL(
    `https://${process.env.FRONTEND_DOMAIN_NAME as string}`
  );
  categoryMetaData.alternates = {
    canonical: `/${categories.join("/")}`,
  };

  return categoryMetaData;
}

export default async function Page({ params, searchParams }: Props) {
  const { categories } = await params;
  const { page } = await searchParams;

  const cat = await retrieveCategoryFromServerSide({
    categorySlug: categories[categories.length - 1],
  });
  const categoryProductsData = cat
    ? await retrieveProductsFromServerSide({
        page: page ? parseInt(page as string) : 1,
        limit: 20,
        categoriesSlugs: cat
          ? cat.subCategories.length === 0
            ? [cat.slug]
            : cat.subCategories
                .flatMap((cat) =>
                  cat.subCategories.length > 0 ? cat.subCategories : [cat]
                )
                .map((cat) => cat.slug)
          : [],
      })
    : null;

  const categoryProducts = categoryProductsData
    ? categoryProductsData.products
    : [];

  if (!cat) notFound();

  return (
    <>
      <Store categoriesSlugs={categories} />

      {/* making products crawlable for seo purposes */}
      <div className="max-w-0 max-h-0 overflow-hidden">
        {categoryProducts.map((product, idx) => (
          <a key={idx} href={getProductPageUrl(product.slug)} className="">
            {product.name}
          </a>
        ))}
      </div>
    </>
  );
}
