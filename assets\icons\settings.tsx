export default function SettingsIcon({
  width = "16",
  height = "17",
}: {
  width?: string;
  height?: string;
  color?: string;
}) {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className="fill-current"
    >
      <path d="M24 10.85V13.15C23.9932 13.3605 23.9158 13.5625 23.7802 13.7236C23.6447 13.8847 23.4588 13.9955 23.2526 14.0381L21.3649 14.3639C21.3049 14.3746 21.2437 14.3778 21.1829 14.3735C20.9616 15.2305 20.6207 16.0521 20.1703 16.8141C20.2174 16.8533 20.2592 16.8984 20.2949 16.9482L21.3985 18.5135C21.5149 18.6894 21.5684 18.8995 21.5504 19.1097C21.5325 19.3199 21.444 19.5179 21.2994 19.6715L19.6705 21.3007C19.5168 21.4446 19.3191 21.5326 19.1093 21.5502C18.8995 21.5679 18.6898 21.5143 18.5142 21.3981L16.9492 20.296C16.8987 20.2606 16.8531 20.2187 16.8134 20.1714C16.0438 20.6257 15.2136 20.9682 14.3476 21.1889C14.364 21.2813 14.364 21.3759 14.3476 21.4684L14.057 23.1503C14.0086 23.385 13.8825 23.5965 13.699 23.7507C13.5156 23.9049 13.2856 23.9927 13.046 24H10.954C10.7144 23.9927 10.4844 23.9049 10.301 23.7507C10.1175 23.5965 9.99143 23.385 9.94304 23.1503L9.65238 21.4684C9.63578 21.376 9.63578 21.2813 9.65238 21.1889C8.78642 20.9682 7.9562 20.6257 7.18659 20.1714C7.14684 20.2181 7.10121 20.2595 7.05084 20.2944L5.48576 21.3981C5.30985 21.5145 5.09977 21.5681 4.88961 21.5501C4.67945 21.5321 4.48152 21.4437 4.32792 21.2991L2.69896 19.6699C2.55541 19.5163 2.46773 19.3189 2.45006 19.1094C2.43239 18.8999 2.48575 18.6906 2.60154 18.5151L3.70508 16.9498C3.74017 16.8995 3.78149 16.8539 3.82805 16.8141C3.3782 16.052 3.03788 15.2304 2.81714 14.3735C2.75629 14.3778 2.69515 14.3745 2.63508 14.3639L0.747405 14.0381C0.541081 13.9957 0.355061 13.8851 0.219431 13.7239C0.0838001 13.5628 0.00648876 13.3606 0 13.15V10.85C0.00648876 10.6394 0.0838001 10.4372 0.219431 10.2761C0.355061 10.1149 0.541081 10.0042 0.747405 9.96193L2.63508 9.6361C2.69516 9.62549 2.75637 9.6228 2.81714 9.62811C3.03811 8.77123 3.37841 7.94967 3.82805 7.18754C3.78163 7.14752 3.74033 7.10192 3.70508 7.05178L2.60154 5.48649C2.48562 5.31078 2.43219 5.10117 2.44987 4.89141C2.46754 4.68164 2.55528 4.48392 2.69896 4.33009L4.32792 2.70092C4.48166 2.55658 4.67956 2.4683 4.88965 2.45033C5.09974 2.43236 5.30975 2.48576 5.48576 2.60189L7.05084 3.70558C7.10131 3.74102 7.14695 3.78291 7.18659 3.83016C7.94844 3.37973 8.76992 3.03883 9.62683 2.81752C9.62145 2.75674 9.62413 2.69551 9.63482 2.63543L9.96061 0.747505C10.0029 0.541153 10.1136 0.355109 10.2747 0.21946C10.4359 0.0838115 10.638 0.00648962 10.8485 0H13.1515C13.362 0.00648962 13.5641 0.0838115 13.7253 0.21946C13.8864 0.355109 13.9971 0.541153 14.0394 0.747505L14.3652 2.63543C14.3756 2.69554 14.3783 2.75673 14.3732 2.81752C15.23 3.03893 16.0515 3.37982 16.8134 3.83016C16.8531 3.78291 16.8987 3.74102 16.9492 3.70558L18.5142 2.60189C18.69 2.48588 18.8997 2.43256 19.1096 2.45053C19.3194 2.4685 19.517 2.55671 19.6705 2.70092L21.2994 4.33009C21.4436 4.4836 21.5318 4.68125 21.5498 4.89109C21.5678 5.10093 21.5144 5.31071 21.3985 5.48649L20.2949 7.05178C20.2592 7.10201 20.2173 7.14761 20.1703 7.18754C20.6207 7.94949 20.9616 8.77108 21.1829 9.62811C21.2436 9.62275 21.3048 9.62543 21.3649 9.6361L23.2526 9.96193C23.4588 10.0045 23.6447 10.1153 23.7802 10.2764C23.9158 10.4375 23.9932 10.6395 24 10.85ZM16.0868 12C16.0865 11.1917 15.8465 10.4017 15.3974 9.72981C14.9482 9.0579 14.3099 8.53427 13.5632 8.22511C12.8165 7.91594 11.9949 7.83514 11.2023 7.9929C10.4097 8.15066 9.68167 8.5399 9.11022 9.11143C8.53877 9.68295 8.14957 10.4111 7.99183 11.2038C7.83409 11.9965 7.91489 12.8182 8.22401 13.565C8.53313 14.3118 9.05669 14.9502 9.72852 15.3994C10.4003 15.8486 11.1903 16.0886 11.9984 16.0889C12.5354 16.0891 13.0671 15.9835 13.5632 15.7781C14.0593 15.5727 14.5101 15.2715 14.8898 14.8918C15.2695 14.512 15.5706 14.0612 15.776 13.565C15.9814 13.0688 16.087 12.537 16.0868 12Z" />
    </svg>
  );
}
