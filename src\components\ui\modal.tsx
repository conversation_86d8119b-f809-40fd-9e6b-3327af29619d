import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import Text from "@/styles/text-styles";
import { useEffect } from "react";
import { CircleX } from "lucide-react";
import { Button } from "./button";

interface Props {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  description: string;
  closeName: string;
  confirmName: string;
  confirmationIsLoading: boolean;
  warning?: string;
}

export function Modal({
  isOpen,
  title,
  description,
  confirmName,
  closeName,
  confirmationIsLoading,
  onClose,
  onConfirm,
  warning,
}: Props) {
  useEffect(() => {
    if (isOpen) document.body.classList.add("overflow-hidden");
    else if (document.body.classList.contains("overflow-hidden"))
      document.body.classList.remove("overflow-hidden");
  }, [isOpen]);

  return (
    <AlertDialog open={isOpen}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <div className="flex justify-between ">
            <AlertDialogTitle>{title}</AlertDialogTitle>
            {warning && warning !== "" && (
              <Text textStyle="TS7" className="text-danger">
                {warning}
              </Text>
            )}
            <Button
              variant={"ghost"}
              onClick={onClose}
              className="absolute top-1 right-4 text-gray px-0"
            >
              <CircleX size={14} />
            </Button>
          </div>
        </AlertDialogHeader>
        {/* <hr className="my-4 border-t border-gray text-sm" /> */}
        <div className="text-center">
          <AlertDialogDescription>{description}</AlertDialogDescription>
        </div>

        <div className="flex justify-center mt-6 space-x-4">
          <Button
            variant="ghost"
            onClick={onClose}
            className="px-6 py-6 rounded-xl bg-gray text-white"
          >
            {closeName}
          </Button>
          <Button
            onClick={onConfirm}
            disabled={confirmationIsLoading}
            className="px-6 py-6 bg-primary rounded-xl hover:transform-none border"
          >
            {confirmName}
          </Button>
        </div>
      </AlertDialogContent>
    </AlertDialog>
  );
}
