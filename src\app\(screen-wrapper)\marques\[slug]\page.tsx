import Store from "@/modules/catalog/components/store";
import { generateBrandMetaData } from "@/modules/catalog/utils/seo/meta-data/brands";
import { getBrandPageUrl } from "@/modules/catalog/utils/urls";
import { Metadata } from "next";

interface Props {
  params: Promise<{
    slug: string;
  }>;
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { slug } = await params;
  const brandMetadata = await generateBrandMetaData({ brandSlug: slug });

  brandMetadata.metadataBase = new URL(
    `https://${process.env.FRONTEND_DOMAIN_NAME as string}`
  );
  brandMetadata.alternates = {
    canonical: getBrandPageUrl(slug),
  };

  return brandMetadata;
}

export default async function Page({ params }: Props) {
  const { slug } = await params;

  return <Store brandSlug={slug} />;
}
