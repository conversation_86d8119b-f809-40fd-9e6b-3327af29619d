import { POST } from "@/lib/http-methods";

import { AxiosError } from "axios";
import { AddressType } from "../../types/addresses";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { refreshToken } from "@/modules/auth/services/refresh-token";

type ResponseType = {
  ok: boolean;
  address?: AddressType;
  status: number;
  error?: string;
};

export default async function createAddressOnServerSide(
  address: { [key: string]: string } //address schema due to the dynmic input extraction
): Promise<ResponseType> {
  const { access } = extractJWTokens();
  const header = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const res = await POST(`/addresses/register`, header, address);

    return {
      ok: true,
      status: 204,
      address: res.data as AddressType,
    };
  } catch (error) {
    const axiosError = error as AxiosError<{ message: string; code: string }>;

    if (axiosError.response?.status === 401) {
      const res = await refreshToken(() => createAddressOnServerSide(address));

      //unauthorized user error is already handled by the user hook
      if (!res)
        return {
          ok: false,
          status: 401,
          error: "Unauthorized!",
        };
      return res;
    } else if (axiosError.response?.status === 400) {
      if (axiosError.response?.data.code === "P1000")
        return {
          ok: false,
          status: 400,
          error: "emailInvalid",
        };
    }
    return {
      ok: false,
      status: 500,
      error: "An unexpected error occurred!",
    };
  }
}
