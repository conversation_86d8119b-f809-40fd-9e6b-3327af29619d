import { cn } from "@/lib/utils";
import React, { HTMLAttributes } from "react";

interface Props extends HTMLAttributes<"div"> {
  children: React.ReactNode | React.ReactNode[];
}

export const screenWrapperStyle =
  "XL:px-[50px] px-2 py-5 max-w-[1500px] w-full";

export default function ScreenWrapper({ children, ...props }: Props) {
  return (
    <div className={cn(screenWrapperStyle, props.className)}>{children}</div>
  );
}
