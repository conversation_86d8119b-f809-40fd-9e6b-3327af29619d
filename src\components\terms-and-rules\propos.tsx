import BreadCrumb from "@/components/breadcrumb";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import Link from "next/link";

export const Propos = () => {
  const t = useTranslations("propos");

  return (
    <div>
      <BreadCrumb />

      <div className="text-3xl font-bold text-primary mt-12 mb-5">
        <Text textStyle="TS2">{t("title")}</Text>
      </div>

      <div className="px-4 L:px-8 py-5 space-y-4">
        <div>
          <Text textStyle="TS5">
            {t.rich("line1", {
              b: (chunk) => <span className="font-bold">{chunk}</span>,
              a: (chunk) => (
                <Link href="/" className="text-primary underline">
                  {chunk}
                </Link>
              ),
            })}
          </Text>
        </div>
        <div>
          <Text textStyle="TS5">
            {t.rich("line2", {
              b: (chunk) => <span className="font-bold">{chunk}</span>,
              a: (chunk) => (
                <Link href="/" className="text-primary underline">
                  {chunk}
                </Link>
              ),
              a1: (chunk) => (
                <Link href="/marques" className="text-primary underline">
                  {chunk}
                </Link>
              ),
            })}
          </Text>
        </div>
        <div>
          <Text textStyle="TS4" className="font-bold">
            {t("line3")}
          </Text>
        </div>
        <div>
          <Text textStyle="TS5">{t("line4")} ...</Text>
        </div>
        <div>
          <Text textStyle="TS5">
            {t.rich("line5", {
              b: (chunk) => <span className="font-bold">{chunk}</span>,
            })}
          </Text>
        </div>

        <div>
          <div>
            <Text textStyle="TS5">
              {t.rich("line6", {
                b: (chunk) => <span className="font-bold">{chunk}</span>,
                a: (chunk) => (
                  <Link href="/" className="text-primary underline font-bold">
                    {chunk}
                  </Link>
                ),
              })}
            </Text>
          </div>
          <div>
            <Text textStyle="TS5">
              {t.rich("line7", {
                b: (chunk) => <span className="font-bold">{chunk}</span>,
              })}
            </Text>
          </div>
          <div>
            <Text textStyle="TS5">
              {t.rich("line8", {
                b: (chunk) => <span className="font-bold">{chunk}</span>,
              })}
            </Text>
          </div>
        </div>
        <div>
          <div>
            <Text textStyle="TS5">
              {t.rich("line9", {
                b: (chunk) => <span className="font-bold">{chunk}</span>,
              })}
            </Text>
          </div>
          <div>
            <Text textStyle="TS5">{t("line10")} </Text>
          </div>
          <div>
            <Text textStyle="TS5">
              {t.rich("line11", {
                b: (chunk) => <span className="font-bold">{chunk}</span>,
              })}
            </Text>
          </div>
        </div>
      </div>
    </div>
  );
};
