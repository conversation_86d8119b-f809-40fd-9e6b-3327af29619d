import useAuthRefresher from "@auth/context/auth-refresher";
import { signIn } from "@/modules/auth/services/sign-in";
import { signUp } from "@/modules/auth/services/sign-up";
import { UserSignUpType } from "@/modules/auth/types";
import { verifyAuthContent } from "@auth/validation/dom-extraction-verification/auth";
import {
  Dispatch,
  FormEvent,
  SetStateAction,
  useEffect,
  useRef,
  useState,
} from "react";

import {
  getSignInStatusWarning,
  getSignUpStatusWarning,
} from "@auth/utils/warnings/server-response-warning";
import { useTranslations } from "next-intl";
import { useQueryClient } from "@tanstack/react-query";
import { useCartStore } from "@/modules/cart/store/cart-store";
import { addCartItemsOnServerSide } from "@/modules/cart/services/cart-items-addition";
import { useRouter } from "next/navigation";

interface AuthWarnings {
  email: string;
  firstName?: string;
  lastName?: string;
  password: string;
  generalWarning: string;
}

export default function useAuth(
  auth: "signIn" | "signUp",
  setEmail: Dispatch<SetStateAction<string>>
) {
  const authRef = useRef<HTMLFormElement>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { refreshUserAuthentication } = useAuthRefresher();
  const queryClient = useQueryClient();
  const t = useTranslations("shared.auth");
  const [warning, setWarning] = useState<AuthWarnings>({
    email: "",
    firstName: "",
    lastName: "",
    password: "",
    generalWarning: "",
  });
  const router = useRouter();
  const cartItems = useCartStore((store) => store.state.cartItems);

  function submitInfo(event: FormEvent) {
    if (authRef.current === null) return;

    event.preventDefault();

    const verificationResult = verifyAuthContent(auth, authRef.current, t);

    setWarning(verificationResult.warning);

    //start sign in or sign up process on server side
    if (verificationResult.ok) {
      setIsLoading(true);

      if (auth == "signUp") {
        signUp(verificationResult.data as UserSignUpType).then((res) => {
          if (res.ok) {
            signIn({
              email: verificationResult.data.email,
              password: verificationResult.data.password,
            }).then(() => {
              //display the confirmation page

              //set email to be displayed in the confirmation page
              setEmail(verificationResult.data.email);

              //cart items addition to server
              const cartItemsToAdd = cartItems.map((cartItem) => ({
                id: cartItem.id,
                quantity: cartItem.cartQuantity,
              }));

              addCartItemsOnServerSide(cartItemsToAdd).catch((error) => {});

              //section changemennt if we've account confirmation phase

              //refresh user authentication state
              refreshUserAuthentication();
              queryClient.invalidateQueries({ queryKey: ["user-data"] });

              setIsLoading(false);
              router.push("/");
            });
          } else {
            const warning = getSignUpStatusWarning(res.status, t);

            setWarning({
              email: warning.email as string,
              password: "",
              generalWarning: warning.generalWarning,
            });
            setIsLoading(false);
          }
        });
      } else {
        signIn(verificationResult.data).then((res) => {
          if (res.ok) {
            // push cart items from locale storage to the server
            // redirect user to home page

            //cart items addition to server and deletion from localstorage if we've cart app

            //refresh user authentication state
            refreshUserAuthentication();

            queryClient.invalidateQueries({ queryKey: ["user-data"] });

            router.push("/");
          } else {
            const warning = getSignInStatusWarning(res.status, t);

            setWarning({
              email: "",
              password: "",
              generalWarning: warning,
            });
          }

          setIsLoading(false);
        });
      }
    }
  }

  //in case we change from sign in to sign up
  useEffect(() => {
    setWarning({
      email: "",
      firstName: "",
      lastName: "",
      password: "",
      generalWarning: "",
    });
  }, [auth]);

  return { warning, submitInfo, isLoading, authRef };
}
