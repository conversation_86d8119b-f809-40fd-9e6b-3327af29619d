import {
  HeroSectionImages,
  HeroSectionImagesInResponse,
  LandingPageContent,
  LandingPageContentInResponseType,
} from "@/types";
import { castToMetaContentType } from "./meta-content";

export default function castToHeroSectionImage(
  image: HeroSectionImagesInResponse
): HeroSectionImages {
  return {
    computerImage: `${process.env.BACKEND_ADDRESS}${image.computerImage}`,
    mobileImage: `${process.env.BACKEND_ADDRESS}${image.mobileImage}`,
    link: image.redirectUrl,
  };
}

export function castToLandingPageContentType(
  landingPageContentInResponse: LandingPageContentInResponseType
): LandingPageContent {
  return {
    id: landingPageContentInResponse.id,
    name: landingPageContentInResponse.name,
    images: landingPageContentInResponse.images.map((image) => ({
      computerImage: `${process.env.BACKEND_ADDRESS}${
        image.computerImage || ""
      }`,
      mobileImage: `${process.env.BACKEND_ADDRESS}${image.mobileImage || ""}`,
      link: image.redirectUrl,
    })),
    sections: landingPageContentInResponse.sections
      ? landingPageContentInResponse.sections.map((section) => ({
          id: section.id,
          title: section.title,
          description: section.description,
        }))
      : [],
    metaContent: castToMetaContentType(
      landingPageContentInResponse.metaContent
    ),
  };
}
