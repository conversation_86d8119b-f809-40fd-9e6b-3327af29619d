import Text from "@/styles/text-styles";
import { InfiniteMovingCards } from "@/components/ui/infinite-moving-cards";
import { cn } from "@/lib/utils";
import { HTMLAttributes } from "react";
import { useTranslations } from "next-intl";
import ReviewContainer from "./review-container";

type ReviewType = { name: string; review: string; rate: number };

export default function CustomersReviews(props: HTMLAttributes<"div">) {
  const t = useTranslations("landingPage.reviewsSection");

  return (
    <section
      className={cn(
        "w-full flex flex-col items-center space-y-8",
        props.className
      )}
    >
      <h4 className="w-full text-primary font-bold">
        <Text textStyle="TS3">{t("title")}</Text>
      </h4>
      <div
        className={`pb-4 px-2 w-full overflow-x-auto flex XL:justify-center M:space-x-8 space-x-4 snap-x snap-mandatory`}
      >
        <InfiniteMovingCards
          items={t.raw("reviews").map((review: ReviewType, idx: number) => (
            <ReviewContainer
              key={idx}
              review={review.review}
              name={review.name}
              rate={review.rate}
            />
          ))}
          direction="right"
          speed="normal"
        />
      </div>
    </section>
  );
}
