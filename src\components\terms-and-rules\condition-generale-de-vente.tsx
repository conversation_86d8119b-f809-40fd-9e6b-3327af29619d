import BreadCrumb from "@/components/breadcrumb";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import Link from "next/link";

export const ConditionsGeneralesVente = () => {
  const t = useTranslations("conditions");

  return (
    <div>
      <BreadCrumb />

      <div className="text-3xl font-bold text-primary mt-12">
        <Text textStyle="TS2">{t("title")}</Text>
      </div>

      <div className="px-4 L:px-8 py-5 space-y-8">
        <div>
          <h2 className="text-xl font-semibold mt-6">
            <Text textStyle="TS4">{t("section1.title")}</Text>
          </h2>
          <Text textStyle="TS5">
            {t.rich("section1.paragraph1", {
              a: (chunk) => (
                <Link
                  href="https://www.akal.tn"
                  className="text-primary underline inline"
                >
                  {chunk}
                </Link>
              ),
            })}{" "}
          </Text>
        </div>

        <div>
          <h2 className="text-xl font-semibold mt-6">
            <Text textStyle="TS4">{t("section2.title")}</Text>
          </h2>
          <Text textStyle="TS5">{t("section2.paragraph1")}</Text>
        </div>

        <div>
          <h2 className="text-xl font-semibold mt-6">
            <Text textStyle="TS4">{t("section3.title")}</Text>
          </h2>
          <Text textStyle="TS5">
            {t.rich("section3.paragraph1", {
              a: (chunk) => (
                <Link
                  href="https://www.akal.tn"
                  className="text-primary underline inline"
                >
                  {chunk}
                </Link>
              ),
              br: () => <br />,
            })}
          </Text>
        </div>

        <div>
          <h2 className="text-xl font-semibold mt-6">
            <Text textStyle="TS4">{t("section4.title")}</Text>
          </h2>
          <Text textStyle="TS5">
            {t.rich("section4.paragraph1", {
              a: (chunk) => (
                <Link
                  href="https://www.akal.tn"
                  className="text-primary underline inline"
                >
                  {chunk}
                </Link>
              ),
              br: () => <br />,
            })}
          </Text>
        </div>

        <div>
          <h2 className="text-xl font-semibold mt-6 space-y-4">
            <Text textStyle="TS4">{t("section5.title")}</Text>
          </h2>
          <Text textStyle="TS5">
            {t.rich("section5.paragraph1", {
              a: (chunk) => (
                <Link
                  href="https://www.akal.tn"
                  className="text-primary underline inline"
                >
                  {chunk}
                </Link>
              ),
              br: () => <br />,
            })}{" "}
          </Text>
        </div>

        <div>
          <h2 className="text-xl font-semibold mt-6">
            <Text textStyle="TS4">{t("section6.title")}</Text>
          </h2>
          <Text textStyle="TS5">
            {t.rich("section6.paragraph1", {
              br: () => <br />,
              li: (chunk) => <li>{chunk}</li>,
              ul: (chunk) => <ul className="list-disc pl-4">{chunk}</ul>,
            })}
          </Text>
        </div>

        <div>
          <h2 className="text-xl font-semibold mt-6">
            <Text textStyle="TS4">{t("section7.title")}</Text>
          </h2>
          <Text textStyle="TS5">
            {t.rich("section7.paragraph1", {
              br: () => <br />,
            })}
          </Text>
        </div>

        <div>
          <h2 className="text-xl font-semibold mt-6">
            <Text textStyle="TS4">{t("section8.title")}</Text>
          </h2>
          <Text textStyle="TS5">
            {t.rich("section8.paragraph1", {
              br: () => <br />,
            })}
          </Text>
        </div>

        <div>
          <h2 className="text-xl font-semibold mt-6">
            <Text textStyle="TS4">{t("section9.title")}</Text>
          </h2>
          <Text textStyle="TS5">
            {t.rich("section9.paragraph1", {
              br: () => <br />,
            })}
          </Text>
        </div>

        <div>
          <h2 className="text-xl font-semibold mt-6">
            <Text textStyle="TS4">{t("section10.title")}</Text>
          </h2>
          <Text textStyle="TS5">
            {t.rich("section10.paragraph1", {
              br: () => <br />,
            })}
          </Text>
        </div>

        <div>
          <h2 className="text-xl font-semibold mt-6">
            <Text textStyle="TS4">{t("section11.title")}</Text>
          </h2>
          <Text textStyle="TS5">
            {t.rich("section11.paragraph1", {
              br: () => <br />,
            })}
          </Text>
        </div>

        <div>
          <h2 className="text-xl font-semibold mt-6">
            <Text textStyle="TS4">{t("section12.title")}</Text>
          </h2>
          <Text textStyle="TS5">{t("section12.paragraph1")}</Text>
        </div>
      </div>
    </div>
  );
};
