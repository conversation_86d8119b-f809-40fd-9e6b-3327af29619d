"use client";

import { useEffect, useState } from "react";
import { Button } from "./ui/button";
import { ChevronUp } from "lucide-react";

export const GoUpButton = () => {
  const [showGoUpButton, setShowGoUpButton] = useState(false);

  const handleScrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  };

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 1200) {
        setShowGoUpButton(true);
      } else {
        setShowGoUpButton(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    showGoUpButton && (
      <Button
        className="w-[50px] h-[50px] flex items-center justify-center bg-white rounded-full border border-primary group hover:bg-primary shadow-lg p-1 z-[9999]"
        onClick={handleScrollToTop}
      >
        <ChevronUp className="h-5 w-5 text-primary group-hover:text-white" />
      </Button>
    )
  );
};
