import { FormEvent, useRef, useState } from "react";
import verifyAddressContent from "../../utils/address-verification";
import { useCheckoutStore } from "../../store/checkout-store";
import createAddressOnServerSide from "../../services/addresses/address-creation";
import { useQueryClient } from "@tanstack/react-query";
import { useTranslations } from "next-intl";
import useAddressSelection from "../../store/address-selection-store";

export default function useAddressCreation(useAddressIdInCheckout = false) {
  const t = useTranslations("shared.warnings");
  const queryClient = useQueryClient();

  const formRef = useRef<HTMLFormElement | null>(null);
  const [warning, setWarning] = useState("");
  const [wrongInputs, setWrongInputs] = useState<string[]>([]); //used to change borders for empty fields
  const [isLoading, setIsLoading] = useState(false);

  const { country, city, countryOptionnalLabels } = useCheckoutStore();

  const selectAddressForCheckout = useAddressSelection(
    (store) => store.selectAddress
  );

  function extractAddressInfo(): Record<string, string> | null {
    //input extraction
    if (formRef.current && country) {
      const formData = new FormData(formRef.current);

      const verificationResult = verifyAddressContent(
        formData,
        countryOptionnalLabels,
        country,
        city,
        t
      );

      if (!verificationResult.valid) {
        setWrongInputs(verificationResult.wrongInputsFinded);
        setWarning(verificationResult.warning);

        return null;
      } else {
        if (warning !== "") setWarning("");
        if (wrongInputs.length > 0) setWrongInputs([]);
      }

      return verificationResult.addressData;
    }

    return null;
  }

  async function createAddress(event?: FormEvent) {
    if (event) event.preventDefault();

    const addressData = extractAddressInfo();
    //data is verified and we're in submition phase
    if (addressData) {
      setIsLoading(true);

      if (warning !== "") setWarning("");

      try {
        const res = await createAddressOnServerSide(addressData);
        let addressId = ""; //it will be used to return the address id for the checkout phase

        if (res.ok) {
          queryClient.invalidateQueries({
            queryKey: ["user-address"],
            exact: false,
          });

          if (!useAddressIdInCheckout)
            document.body.scrollIntoView({ behavior: "smooth" });
          else if (res.address) addressId = res.address.id;

          if (formRef.current) formRef.current.reset();
        } else {
          if (res.error === "emailInvalid") setWarning(t("emailInvalid"));
          else setWarning(t("serverError"));
        }

        setIsLoading(false);

        //returning address id to use in the chekout and select in case of checkout error
        if (useAddressIdInCheckout && addressId !== "") {
          selectAddressForCheckout(addressId);
          return addressId;
        }
      } catch {
        return "";
      }
    } else {
      const addressSection = document.getElementById("addressCreation");
      if (addressSection) addressSection.scrollIntoView({ behavior: "smooth" });
    }

    return "";
  }

  return {
    formRef,
    extractAddressInfo,
    wrongInputs,
    warning,
    createAddress,
    isLoading,
  };
}
