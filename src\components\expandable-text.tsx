"use client";

import { HTMLAttributes, useRef, useState } from "react";
import { ChevronDown, ChevronUp } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { cn } from "@/lib/utils";
import { TextStyle } from "@/styles/text-styles";
import DOMPurify from "dompurify";

interface Props extends HTMLAttributes<"div"> {
  text: string;
}

export default function ExpandableText({ text, className = "" }: Props) {
  const t = useTranslations("shared.buttons");

  const [isExpanded, setIsExpanded] = useState(false);
  const componentRef = useRef<HTMLDivElement>(null);

  const toggleExpanded = () => {
    if (isExpanded) {
      // When collapsing, scroll to the component accounting for fixed navbar
      if (componentRef.current) {
        const elementTop = componentRef.current.offsetTop;
        const offsetPosition = elementTop - 250; // Account for 120px fixed navbar

        window.scrollTo({
          top: offsetPosition,
          behavior: "smooth",
        });
      }
    }
    setIsExpanded(!isExpanded);
  };

  return (
    <div ref={componentRef} className={cn("space-y-3", className)}>
      <div className="relative overflow-hidden">
        <div
          className={cn(
            "text-gray prose prose-sm max-w-none prose-a:underline prose-a:decoration-primary prose-a:text-primary",
            TextStyle["TS6"],
            {
              "line-clamp-3": !isExpanded,
            }
          )}
          dangerouslySetInnerHTML={{
            __html: DOMPurify.sanitize(text),
          }}
        ></div>

        {/* Gradient overlay when collapsed */}
        {!isExpanded && (
          <div className="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-t from-primary-muted to-transparent pointer-events-none" />
        )}
      </div>

      {
        <Button
          variant="ghost"
          onClick={toggleExpanded}
          className="group p-0 h-auto font-medium text-primary hover:text-primary/80 hover:bg-transparent transition-all duration-300"
        >
          <span className="flex items-center gap-1">
            {isExpanded ? t("seeLess") : t("seeMore")}
            <div className="transition-transform duration-300 group-hover:scale-110">
              {isExpanded ? (
                <ChevronUp className="w-4 h-4 transition-transform duration-300" />
              ) : (
                <ChevronDown className="w-4 h-4 transition-transform duration-300" />
              )}
            </div>
          </span>
        </Button>
      }
    </div>
  );
}
