import { CircleX } from "lucide-react";
import { Button } from "@/components/ui/button";
import Text from "@/styles/text-styles";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { cn } from "@/lib/utils";
import { useTranslations } from "next-intl";
import { useProductsFilteringStore } from "../../store/products-filter";

interface Props {
  item: {
    id: string;
    name: string;
  };
  type: "category" | "subCategory" | "brand" | "search";
}

export const AppliedFilter = ({ item, type }: Props) => {
  const { categories, setCategories, removeBrand, setSearch, applyFilter } =
    useProductsFilteringStore();
  const router = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const t = useTranslations("filtersPage");

  const handleRemoveItem = (id: string) => {
    switch (type) {
      case "category": {
        const category = categories.find((cat) => cat.id === id);

        if (category) {
          category.selected = false;
          setCategories([...categories]);

          applyFilter();
        }
        break;
      }
      case "subCategory":
        for (const category of categories) {
          const subCategory = category.subCategories.find(
            (cat) => cat.id === id
          );

          if (subCategory) {
            subCategory.selected = false;
            setCategories([...categories]);

            applyFilter();
            break;
          }
        }

        break;
      case "brand":
        removeBrand(id);

        applyFilter();

        break;
      case "search": {
        const params = new URLSearchParams(searchParams.toString());
        params.delete("search");

        const newUrl = `${pathname}?${params.toString()}`;
        router.replace(newUrl);

        setSearch("");
        break;
      }
    }
  };

  return (
    <div className="flex gap-3 items-center rounded-full px-2 bg-light-primary L:whitespace-nowrap">
      <Text
        textStyle="TS7"
        className={cn("font-semibold L:block ", {
          hidden: type === "search",
        })}
      >
        {t.raw(type)}:
      </Text>
      <Text textStyle="TS7">{item.name}</Text>
      <Button
        variant={"ghost"}
        onClick={() => handleRemoveItem(item.id)}
        className="text-primary px-0"
      >
        <CircleX size={14} />
      </Button>
    </div>
  );
};
