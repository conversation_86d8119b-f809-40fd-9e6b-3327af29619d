import { useQuery } from "@tanstack/react-query";
import { usePathname } from "next/navigation";
import { retrieveUserAddresses } from "../../services/addresses/addresses-extraction";

export default function useAddresses() {
  const pathname = usePathname();
  const { data, isLoading } = useQuery({
    queryKey: ["user-address"],
    queryFn: () => retrieveUserAddresses(),
    enabled: ![
      "mon-compte",
      "mon-compte/commandes",
      "mon-compte/settings",
    ].every((disabledPathname) => pathname.endsWith(disabledPathname)), //data in those pages should not be fetched
  });

  return {
    addresses: data ? data : null,
    addressesAreLoading: isLoading,
  };
}
