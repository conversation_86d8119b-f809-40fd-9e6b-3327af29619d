import { DELETE } from "@/lib/http-methods";
import { refreshToken } from "@/modules/auth/services/refresh-token";
import extractJWTokens from "@/modules/auth/utils/jwt/extract-tokens";
import { AxiosError } from "axios";
import { CustomError } from "@/utils/custom-error";

export async function deleteWishedItemOnServerSide(itemId: string) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    await DELETE(`/favourites/products/${itemId}`, headers);
    return { ok: true };
  } catch (error) {
    const axiosError = error as AxiosError;
    if (axiosError.response?.status == 401) {
      const res = await refreshToken(() =>
        deleteWishedItemOnServerSide(itemId)
      );
      if (!res) throw new CustomError("Unauthorized", 401);
      return res;
    } else if (axiosError.response?.status == 404) {
      throw new CustomError("Product Not Found!", 404);
    } else {
      throw new CustomError("Server Error!", 500);
    }
  }
}

export async function clearWishlistOnServerSide(wishlistItemIds: string[]) {
  const { access } = extractJWTokens();
  const headers = {
    Authorization: `Bearer ${access}`,
  };

  try {
    const deletePromises = wishlistItemIds.map(async (itemId) => {
      try {
        await DELETE(`/favourites/products/${itemId}`, headers);
        return { success: true, itemId };
      } catch (error) {
        const axiosError = error as AxiosError;
        if (axiosError.response?.status == 401) {
          const res = await refreshToken(() =>
            DELETE(`/favourites/products/${itemId}`, headers)
          );
          if (!res) throw new CustomError("Unauthorized", 401);
          return { success: true, itemId };
        } else if (axiosError.response?.status == 404) {
          return { success: true, itemId };
        } else {
          throw new CustomError("Server Error!", 500);
        }
      }
    });

    await Promise.all(deletePromises);
    return { ok: true };
  } catch (error) {
    throw error;
  }
}
