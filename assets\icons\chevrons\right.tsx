interface Props {
  color?: string;
}

export default function ChevronRight({ color = "black" }: Props) {
  return (
    <svg
      width="10"
      height="17"
      viewBox="0 0 10 17"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.56557 8.49979L0.374649 2.31374C-0.0832163 1.85587 -0.0832163 1.11549 0.374649 0.6625C0.832514 0.209505 1.57289 0.209505 2.03076 0.6625L9.04486 7.67174C9.48812 8.11499 9.49786 8.82614 9.07896 9.28401L2.03563 16.342C1.8067 16.5709 1.5047 16.6829 1.20757 16.6829C0.910449 16.6829 0.608453 16.5709 0.37952 16.342C-0.0783451 15.8841 -0.0783451 15.1437 0.37952 14.6907L6.56557 8.49979Z"
        fill={color}
      />
    </svg>
  );
}
