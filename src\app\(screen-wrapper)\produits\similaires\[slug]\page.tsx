import { cn } from "@/lib/utils";
import ProductsLists from "@/modules/catalog/components/products/products-list";

interface Props {
  params: Promise<{ slug: string }>;
}

export default async function PromotionPage({ params }: Props) {
  const { slug } = await params;

  return (
    <div className={cn("w-full flex flex-col gap-8")}>
      <ProductsLists type="similarProducts" similarProductSlug={slug} />
    </div>
  );
}
