import { cn } from "@/lib/utils";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import ScreenWrapper from "../screen-wrapper";
import { Carousel, CarouselContent, CarouselItem } from "../ui/carousel";
import useCarouselIndicator from "@/hooks/use-carousel-indicator";

export default function TopNotificationBar() {
  const t = useTranslations("shared.navbar.topNotificationBar");
  const { setApi } = useCarouselIndicator({
    timer: 10000,
    autoChangement: true,
  });

  return (
    <div className="relative">
      <Carousel
        setApi={setApi}
        opts={{ loop: true }}
        className={cn(
          "w-full py-2 flex justify-center items-center M:space-x-3 space-x-1 bg-gradient-to-tr from-primary to-blue text-white",
          ScreenWrapper
        )}
      >
        <CarouselContent className="w-full flex">
          {Array.from({ length: 2 }).map((_, idx) => (
            <CarouselItem
              key={idx}
              className="w-full flex items-center justify-center"
            >
              <Text textStyle="TS7" className="text-center">
                {t.rich(idx === 0 ? "title" : "secondTitle", {
                  b: (chunk) => <span className="font-bold">{chunk}</span>,
                })}
              </Text>
            </CarouselItem>
          ))}
        </CarouselContent>
      </Carousel>
    </div>
  );
}
