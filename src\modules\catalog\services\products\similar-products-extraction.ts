import { GET } from "@/lib/http-methods";
import { PaginationType } from "@/types";
import { ProductInResponseType } from "../../types/products";
import { castToProductType } from "../../utils/types-casting/products";

interface Params {
  page: number;
  limit: number;
  productSlug: string;
}

export async function retrieveSimilarProductsFromServerSide({
  page,
  limit,
  productSlug,
}: Params) {
  try {
    const res = await GET(
      `/products/similar/${productSlug}?page=${page}&limit=${limit}`,
      {}
    );

    return {
      pagination: res.data.pagination as PaginationType,
      products: (res.data.data as ProductInResponseType[]).map(
        (productInResponse) => castToProductType(productInResponse)
      ),
    };
  } catch (error) {
    return null;
  }
}
