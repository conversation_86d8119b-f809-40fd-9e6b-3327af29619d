# akal

#verifying the webhooks
**akal** is an e-commerce platform designed to help users manage their shopping carts, view products, and streamline the checkout process.

---

## Features

- **Product Viewing**: Browse products with options to filter by categories and brands.
- **Cart Management**: Add, remove, and update items in your cart.
- **Session-Based Management**: Ensure all actions are preserved across lifetime sessions.
- **Filtering and Pagination**: Easily navigate through products with filters and pagination.
- **Dynamic Landing Page**: Display personalized and dynamic content on the landing page based on admin-defined preferences.
- **Checkout with Address Addition**: Seamlessly proceed to checkout, including adding or updating your delivery address.

---

## Installation

### Prerequisites

- **Node.js**: Ensure you have Node.js installed (version 16 or higher).
- **npm or yarn**: A package manager to install dependencies.

### Steps

1. Clone the repository:

   ```bash
   git clone https://github.com/tawer-tn/akal.git
   cd akal-website
   git clone https://github.com/tawer-tn/akal.git
   cd akal-website
   ```

2. Install dependencies:

   ```bash
   npm install
   # or
   yarn install
   ```

3. Run the development server:

   ```bash
   npm run dev
   # or
   yarn dev
   ```

4. Open your browser and navigate to:
   ```
   http://localhost:3000
   ```

---

## Usage

1. **Browse Products**: Navigate through products categorized by type and brand.
2. **Manage Cart**: Add items to your cart, adjust quantities, or remove items.
3. **Filter and Search**: Use filters and search functionality to find specific products.
4. **Personalized Landing**: View dynamic content on the landing page, customized by admin-defined preferences.
5. **Checkout**: Add or update your address and proceed to checkout.

---

## Scripts

- **Start Development Server**:

  ```bash
  npm run dev
  ```

- **Build for Production**:

  ```bash
  npm run build
  ```

- **Run Production Server**:

  ```bash
  npm start
  ```

- **Lint Code**:
  ```bash
  npm run lint
  ```

---

## Technologies Used

- **Next.js**: Framework for the frontend and backend.
- **Tailwind CSS**: Styling.
- **TanStack Query**: Data fetching and caching.
- **Axios**: HTTP client for API requests.

---

## Contributing

We welcome contributions! Follow these steps to contribute:

1. Fork the repository.
2. Create a new branch:
   ```bash
   git checkout -b feature-name
   ```
3. Commit your changes:
   ```bash
   git commit -m "Add new feature"
   ```
4. Push to your branch:
   ```bash
   git push origin feature-name
   ```
5. Open a pull request.

---

## Contact

For support or inquiries, contact us at [<EMAIL>](mailto:<EMAIL>).

---

Happy shopping with akal!
