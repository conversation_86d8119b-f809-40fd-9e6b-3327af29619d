import useUserStore from "@/modules/auth/store/user-store";
import { updateCartItemOnServerSide } from "../services/cart-item-edition";
import { useCartStore } from "../store/cart-store";
import { ProductItemType } from "../types/products";
import { CustomError } from "@/utils/custom-error";
import { addCartItemOnServerSide } from "../services/cart-item-addition";

export function updateProductQuantity(
  cartItems: ProductItemType[],
  id: string,
  quantity: number,
  checkAuthentication?: boolean
): null | ProductItemType[] {
  //check if the product already exist to update its quantity if needed
  const availableProduct = cartItems.find(
    (productItem) => productItem.id === id
  );

  if (availableProduct) {
    availableProduct.cartQuantity = quantity;
    //update product on server side if user is authenticated
    if (checkAuthentication && availableProduct.productItemCartId) {
      updateProductQuantityOnServerSide(
        availableProduct.cartQuantity,
        availableProduct.productItemCartId,
        quantity
      );
    }

    return [...cartItems];
  }

  return null;
}

export function updateProductQuantityOnServerSide(
  currentQuantity: number,
  id: string,
  targetQuantity: number
) {
  //product already exist and its quantity will be changed
  //checking if use is authenticated to store product on server side also
  const { user } = useUserStore.getState();
  if (user && user.isAuthenticated) {
    //creating product item on the server side also

    updateCartItemOnServerSide({
      id,
      quantity: targetQuantity,
    }).catch((error: CustomError) => {
      //setting the previous quantity
      if (error.status !== 404)
        //error other then product not found on server side
        useCartStore
          .getState()
          .actions.updateProductItemQuantity(id, currentQuantity, false);
      else {
        //product not found on server side so we need to create on the server side
        //creating product on server side
        addCartItemOnServerSide({ id, quantity: targetQuantity }).catch(() => {
          //deletion product from the current cart
          useCartStore.getState().actions.removeProductItem(id, false);
        });
      }
    });
  }
}
