"use client";
import { screenWrapperStyle } from "@/components/screen-wrapper";
import { cn } from "@/lib/utils";
import AuthServicesSection from "@/modules/auth/components/auth-services-section";

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="w-full flex justify-center">
      <div
        className={cn(
          "min-h-[60vh] flex md:space-x-5 w-full",
          screenWrapperStyle
        )}
      >
        <div className="flex-1 flex items-center justify-center bg-white">
          <div className="w-full flex justify-center">
            <div className="w-full">{children}</div>
          </div>
        </div>

        <div className="hidden md:block flex-1 ">
          <AuthServicesSection />
        </div>
      </div>
    </div>
  );
}
