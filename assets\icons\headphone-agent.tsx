interface Props {
  color?: string;
}

export default function HeadphoneAgent({ color = "white" }: Props) {
  return (
    <svg
      width="41"
      height="41"
      viewBox="0 0 41 41"
      fill={color}
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_3686_751)">
        <path
          d="M35.8756 13.6667C36.7818 13.6667 37.6508 14.0267 38.2916 14.6674C38.9323 15.3082 39.2923 16.1772 39.2923 17.0834V23.9167C39.2923 24.8229 38.9323 25.6919 38.2916 26.3326C37.6508 26.9734 36.7818 27.3334 35.8756 27.3334H34.0614C33.6449 30.6361 32.0375 33.6733 29.5407 35.875C27.044 38.0768 23.8295 39.2916 20.5007 39.2917V35.875C23.2191 35.875 25.8262 34.7951 27.7485 32.8729C29.6707 30.9506 30.7506 28.3435 30.7506 25.625V15.375C30.7506 12.6566 29.6707 10.0494 27.7485 8.12719C25.8262 6.20494 23.2191 5.12503 20.5007 5.12503C17.7822 5.12503 15.1751 6.20494 13.2528 8.12719C11.3306 10.0494 10.2507 12.6566 10.2507 15.375V27.3334H5.12565C4.21949 27.3334 3.35045 26.9734 2.7097 26.3326C2.06895 25.6919 1.70898 24.8229 1.70898 23.9167V17.0834C1.70898 16.1772 2.06895 15.3082 2.7097 14.6674C3.35045 14.0267 4.21949 13.6667 5.12565 13.6667H6.9399C7.35679 10.3643 8.96441 7.32754 11.4611 5.12619C13.9578 2.92483 17.1721 1.71021 20.5007 1.71021C23.8292 1.71021 27.0435 2.92483 29.5402 5.12619C32.0369 7.32754 33.6445 10.3643 34.0614 13.6667H35.8756ZM13.2573 26.9661L15.0681 24.0687C16.6963 25.0887 18.5794 25.6281 20.5007 25.625C22.4219 25.6281 24.305 25.0887 25.9332 24.0687L27.744 26.9661C25.5731 28.3262 23.0624 29.0456 20.5007 29.0417C17.9389 29.0456 15.4282 28.3262 13.2573 26.9661Z"
          fill={color}
        />
      </g>
      <defs>
        <clipPath id="clip0_3686_751">
          <rect width="41" height="41" fill={color} />
        </clipPath>
      </defs>
    </svg>
  );
}
