import { MetadataRoute } from "next";

export default function robots(): MetadataRoute.Robots {
  const domain = process.env.FRONTEND_DOMAIN_NAME || "ecommerce-core.tdg.tn";

  return process.env.NEXT_PUBLIC_ENV === "preprod"
    ? {
        rules: {
          userAgent: "*",
          disallow: "/",
        },
        sitemap: `https://${domain}/sitemap.xml`,
      }
    : {
        rules: [
          {
            userAgent: "*",
            disallow: [
              "/paiement",
              "/mon-compte",
              "/mon-compte/commandes",
              "/mon-compte/info",
              "/mon-compte/adresses",
              "/mon-compte/parametres",
            ],
          },
        ],
        sitemap: "https://parastore.tn/sitemap.xml",
        host: "https://parastore.tn",
      };
}
