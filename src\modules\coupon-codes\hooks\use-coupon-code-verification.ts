import { useEffect, useState } from "react";
import { CustomError } from "../../../utils/custom-error";
import { useTranslations } from "next-intl";
import useValidatedCouponCode from "../store/coupon-code-validation";
import { verifyCouponCodeOnServerSide } from "../services/coupon-code-verification";
import { useCartStore } from "@/modules/cart/store/cart-store";
import {
  validateCouponOnPromotions,
  getProductNamesNotCoveredByCoupon,
} from "../utils/coupon-promotion-validation";

export default function useCouponCodeVerification() {
  const [couponCode, setCouponCode] = useState("");
  const [error, setError] = useState<string>("");
  const [promotionWarning, setPromotionWarning] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const t = useTranslations("shared.warnings.couponCodes");
  const tPromotion = useTranslations(
    "shared.warnings.couponCodes.promotionRestrictions"
  );
  const { cartItems } = useCartStore((store) => store.state);
  const { setValidatedCouponCode, validatedCouponCode } =
    useValidatedCouponCode((store) => store);

  async function verifyCouponCode() {
    if (error !== "") setError("");
    if (promotionWarning !== "") setPromotionWarning("");

    if (couponCode === "") return;
    setIsLoading(true);

    try {
      const validatedCouponCodeFromServerSide =
        await verifyCouponCodeOnServerSide(couponCode);

      if (validatedCouponCodeFromServerSide) {
        // Validate coupon against products with promotions
        const promotionValidation = validateCouponOnPromotions(
          validatedCouponCodeFromServerSide,
          cartItems
        );

        if (
          promotionValidation.allProductsHavePromotions &&
          !promotionValidation.canApplyToAnyProduct
        ) {
          setError(tPromotion("cannotApplyToAnyProduct"));
          return;
        }

        if (
          promotionValidation.productsWithPromotionsNotCovered.length > 0 &&
          promotionValidation.canApplyToAnyProduct
        ) {
          const productNames = getProductNamesNotCoveredByCoupon(
            promotionValidation.productsWithPromotionsNotCovered
          );
          setPromotionWarning(
            tPromotion("partiallyApplicable", { productNames })
          );
          setValidatedCouponCode(validatedCouponCodeFromServerSide);
          return;
        }

        setValidatedCouponCode(validatedCouponCodeFromServerSide);
      }
    } catch (error) {
      const customError = error as CustomError;

      if (customError.status === 404) setError(t("doesntExist"));
      else if (customError.code === "P9001") {
        setError(t("usersLimitPassed"));
      } else setError(t("expiredCoupon"));
    } finally {
      setIsLoading(false);
    }
  }

  useEffect(() => {
    return () => {
      setValidatedCouponCode(null);
    };
  }, []);

  return {
    verifyCouponCode,
    couponCode,
    setCouponCode,
    isLoading,
    error,
    promotionWarning,
    validatedCouponCode,
  };
}
