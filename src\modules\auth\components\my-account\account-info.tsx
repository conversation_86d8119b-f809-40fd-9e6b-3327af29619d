"use client";
import Text from "@/styles/text-styles";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useTranslations } from "next-intl";
import useNameChangement from "../../hooks/use-name-changement";
import WarnInput from "@/components/input/warn-input";
import { cn } from "@/lib/utils";
import { useEffect } from "react";
import useUserStore from "../../store/user-store";

export default function AccountInfo() {
  const { user, isLoading } = useUserStore((store) => store);

  const { submitName, formRef, warning, nameChanged, setNewName, newName } =
    useNameChangement();

  // const {
  //   isLoading: pointsSubmissionIsLoading,
  //   error,
  //   submitPoints,
  //   coupon,
  // } = usePointsToCoupon();

  const t = useTranslations("accountPage.accountInfo");
  // const [isCouponDialogOpen, setIsCouponDialogOpen] = useState(false);
  // const { pointsRate } = usePointsRate();

  useEffect(() => {
    if (user?.name) {
      setNewName(user.name);
    }
  }, [user?.name]);

  // useEffect(() => {
  //   if (coupon) {
  //     setIsCouponDialogOpen(true);
  //   }
  // }, [coupon]);

  return !isLoading ? (
    <div className="flex flex-col space-y-7">
      <Text textStyle="TS2" className="text-primary font-bold text-xl">
        {t("title")}
      </Text>

      <form ref={formRef} className="flex flex-col space-y-7">
        {warning.getGeneralWarning !== "" ? (
          <Text textStyle="TS6" className="text-danger">
            {warning.getGeneralWarning}
          </Text>
        ) : null}

        <div className="space-y-7">
          <label className="M:w-fit w-full flex flex-col space-y-2">
            <Text
              textStyle="TS5"
              className="font-bold text-primary font-tajawal"
            >
              {t("name")}
            </Text>
            <WarnInput
              name="newName"
              className={cn(
                "text-primary font-tajawal extraL:min-w-[450px] M:min-w-[350px] border border-primary rounded-lg bg-opacity-[54%]"
              )}
              type="text"
              value={newName || ""}
              onChange={(event: any) => setNewName(event.target.value)}
              warning={warning.newName}
              eyeColor="white"
            />
          </label>

          <div className="flex flex-col space-y-3 w-fit extraL:min-w-[450px] M:min-w-[350px] rounded-sm">
            <Text textStyle="TS5" className="text-primary font-semibold">
              {t("email")}
            </Text>
            <Input
              value={user?.email}
              disabled
              className="border border-gray-light p-6 text-primary"
            />
          </div>

          <Button
            className="bg-primary text-white rounded-xl group h-11 w-fit extraL:min-w-[450px] M:min-w-[350px]"
            onClick={submitName}
          >
            {nameChanged ? (
              <Text
                textStyle="TS5"
                className="text-white font-tajawal group-hover:text-primary"
              >
                {t("buttons.changementConfirmed")}
              </Text>
            ) : (
              <Text
                textStyle="TS5"
                className="text-white font-tajawal group-hover:text-primary"
              >
                {t("buttons.save")}
              </Text>
            )}
          </Button>

          {/* {user && (
            <div className="space-y-3">
              {error !== "" ? (
                <Text textStyle="TS6" className="text-danger">
                  {error}
                </Text>
              ) : null}

              <Text textStyle="TS6" className="font-bold text-primary">
                {t("mesPoints")} :
              </Text>
              <div className="flex justify-between items-center bg-gray-100 rounded-2xl border border-primary ps-4">
                <Text textStyle="TS6" className="text-primary">
                  {`${user?.points} `}
                  {t.rich("points", {
                    amount: (chunk) => (
                      <span>{(user?.points * pointsRate).toFixed(3)}</span>
                    ),
                  })}
                </Text>
                <Button
                  className="bg-primary text-white rounded-xl px-4 py-2"
                  disabled={pointsSubmissionIsLoading || user.points == 0}
                >
                  {t("convert")}
                </Button>
              </div>
            </div>
          )} */}
        </div>
      </form>
      {/* {coupon && (
        <CouponPopUp
          amount={coupon.amount}
          percentage={coupon.percentage}
          code={coupon.code}
          date={coupon.date}
          isOpen={isCouponDialogOpen}
          setIsOpen={setIsCouponDialogOpen}
          forever={coupon.forever}
          type={coupon.type}
        />
      )} */}
    </div>
  ) : (
    <div className="flex flex-col space-y-7 p-6 bg-white rounded-lg shadow-md">
      <Skeleton className="w-40 h-6" />
      <div className="grid grid-cols-2 gap-4">
        <Skeleton className="w-full h-10" />
        <Skeleton className="w-full h-10" />
      </div>
      <Skeleton className="w-full h-10" />
      <Skeleton className="w-full h-10" />
      <Skeleton className="w-full h-10" />
      <Skeleton className="w-full h-12" />
    </div>
  );
}
