import { useState } from "react";
import { CustomError } from "../../../utils/custom-error";
import { useTranslations } from "next-intl";
import { pointsToCouponOnServerSide } from "../services/points-to-coupon";
import dayjs, { Dayjs } from "dayjs";
import { useQueryClient } from "@tanstack/react-query";

export default function usePointsToCoupon() {
  const [error, setError] = useState<string>("");
  const queryClient = useQueryClient();
  const [isLoading, setIsLoading] = useState(false);
  const [coupon, setCoupon] = useState<{
    amount?: number;
    percentage?: number;
    type: string;
    forever: boolean;
    code: string;
    date?: Dayjs;
  } | null>(null);
  const t = useTranslations("shared.warnings");

  async function submitPoints(points: number) {
    if (error !== "") setError("");

    if (points === 0) return;
    setIsLoading(true);

    try {
      const res = await pointsToCouponOnServerSide(points);

      if (res?.code) {
        const couponData = {
          [res.discount.type === "percentage" ? "percentage" : "amount"]:
            res.discount.value,
          type: res.discount.type,
          forever: res.period.forever,
          code: res.code,
          date: res.period.to ? dayjs(res.period.to) : undefined,
        };
        setCoupon(couponData);
      }

      queryClient.invalidateQueries({ queryKey: ["coupons"] });
      queryClient.invalidateQueries({ queryKey: ["user-data"] });
    } catch (error) {
      const customError = error as CustomError;
      if (customError.code === "P5062") {
        setError(t("pointsExceedsPointsInAccount"));
      } else if (customError.code === "P1000") setError(t("invalidData"));
      else setError(t("serverError"));
    } finally {
      setIsLoading(false);
    }
  }

  return {
    isLoading,
    error,
    submitPoints,
    coupon,
  };
}
