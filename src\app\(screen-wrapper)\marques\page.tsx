"use client";
import PaginationMangement from "@/components/pagination/pagination-management";
import { Skeleton } from "@/components/ui/skeleton";
import BrandContainer from "@/modules/catalog/components/brands/brand-container";
import useBrands from "@/modules/catalog/hooks/brands/use-brands";
import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";

export default function Page() {
  const { brands, brandsAreLoading, page, pagesNumber, setPage } = useBrands({
    limit: 30,
    paginationAffectUrl: true,
  });

  const t = useTranslations("brandsPage");

  return (
    <div className="flex flex-col gap-8 p-4">
      <Text
        data-test=""
        textStyle="TS3"
        className="text-neutral font-semibold mt-8 text-primary"
      >
        {t.raw("ourBrands")}
      </Text>
      <div className="grid S:grid-cols-2 M:grid-cols-3 L:grid-cols-4 XL:grid-cols-5 gap-5">
        {brandsAreLoading ? (
          <>
            {Array.from({ length: 20 }).map((_, idx) => (
              <Skeleton key={idx} className="h-[100px] w-full rounded-[32px]" />
            ))}
          </>
        ) : (
          brands?.map((brand, idx) => (
            <BrandContainer key={idx} brand={brand} />
          ))
        )}
      </div>
      <div className="flex justify-center mt-4">
        <PaginationMangement
          currentPage={page}
          pagesNumber={pagesNumber}
          changePage={setPage}
        />
      </div>
    </div>
  );
}
