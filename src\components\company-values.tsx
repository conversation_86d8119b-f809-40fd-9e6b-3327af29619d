import Text from "@/styles/text-styles";
import { cn } from "@/lib/utils";
import { HTMLAttributes } from "react";
import CartIcon from "@assets/icons/cart";
import PrecentageIcon from "@assets/icons/percentage";
import PhoneIcon from "@assets/icons/phone";
import CircularText from "@assets/icons/circul";
import TruckDeliveryIcon from "@assets/icons/truck/delivery";
import { useTranslations } from "next-intl";

const valuesIcons = [
  <CartIcon key={0} />,
  <PrecentageIcon key={1} />,
  <PhoneIcon key={2} />,
  <TruckDeliveryIcon key={3} />,
];

export default function CompanyValues(props: HTMLAttributes<"div">) {
  const t = useTranslations("landingPage.whyUs");

  return (
    <section
      className={cn(
        "w-full flex flex-col items-center space-y-8 my-16",
        props.className
      )}
    >
      {/* <h3 className="w-full text-primary font-bold">
        <Text textStyle="TS2">{t("title")}</Text>
      </h3> */}
      <div className="grid L:grid-cols-4 grid-cols-2 gap-2">
        {t
          .raw("values")
          .map((value: { title: string; description: string }, idx: number) => (
            <div
              key={idx}
              className="flex flex-col items-center space-y-8 mt-5"
            >
              <div className="relative flex items-center justify-center">
                <CircularText text={value.title} />
                <div className="w-20 h-20 rounded-full bg-primary text-white flex justify-center items-center">
                  <span className="fill-white">{valuesIcons[idx]}</span>
                </div>
              </div>
              <div className="flex flex-col items-center space-y-2 text-center">
                <Text textStyle="TS4" className="font-bold">
                  {value.title}
                </Text>
                <Text textStyle="TS5" className="text-primary">
                  {value.description}
                </Text>
              </div>
            </div>
          ))}
      </div>
    </section>
  );
}
