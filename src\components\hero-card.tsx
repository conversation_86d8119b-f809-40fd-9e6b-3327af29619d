import Text from "@/styles/text-styles";
import { useTranslations } from "next-intl";
import Image from "next/image";

export default function HeroCard() {
  const t = useTranslations("shared.sections.healthInvestement");
  return (
    <section className="w-full relative flex justify-center items-center">
      <div className="w-full bg-primary regularL:aspect-[1206/300] L:aspect-[1206/400] aspect-[1206/500]" />
      <Image
        alt="leaft-effect"
        src="/natural-health.png"
        width={1206}
        height={689}
        className="w-full h-full absolute right-0 left-0 top-0 bottom-0 opacity-50 overflow-hidden"
      />
      <Image
        alt="leaft-effect"
        src="/background-effects/leaf.svg"
        width={369}
        height={303}
        className="absolute left-0 bottom-0 overflow-hidden"
      />
      <Image
        alt="leaft-effect"
        src="/background-effects/leaf.svg"
        width={369}
        height={303}
        className="absolute right-0 top-0 rotate-180 overflow-hidden"
      />
      <div className="top-0 bottom-0 absolute flex flex-col justify-center items-center">
        <h1 className="relative w-fit text-white before:content-[''] before:absolute before:z-10 XL:before:bottom-4 before:bottom-4 XL:before:-right-8 XL:before:-left-8 before:-right-4 before:-left-4 XL:before:h-5 before:h-3 before:bg-secondary">
          <Text textStyle="TS1" className="relative z-30">
            {t("title")}
          </Text>
        </h1>

        <Text textStyle="TS4" className="text-white px-4 font-bold text-center">
          {t.rich("description", {
            br: () => <br />,
            underline: (chunk) => (
              <span className="relative whitespace-nowrap before:content-[''] before:absolute before:z-10 before:bottom-0 XL:before:-right-1 XL:before:-left-1 before:right-0 before:left-0  XL:before:h-2 before:h-1 before:bg-secondary">
                <span className="relative z-30">{chunk}</span>
              </span>
            ),
          })}
        </Text>
      </div>
    </section>
  );
}
