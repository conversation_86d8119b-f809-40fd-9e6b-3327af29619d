export default function YahooIcon({
  width = "35",
  height = "35",
}: {
  width?: string;
  height?: string;
}) {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 36 36"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clipPath="url(#clip0_3524_729)">
        <path
          d="M30.6 0H5.4C2.41766 0 0 2.41766 0 5.4V30.6C0 33.5823 2.41766 36 5.4 36H30.6C33.5823 36 36 33.5823 36 30.6V5.4C36 2.41766 33.5823 0 30.6 0Z"
          fill="#5F01D1"
        />
        <path
          d="M14.2734 28.4062H9.91406L11.6719 24.2578L6.82031 12.6562H11.25L13.8516 19.3359L16.4531 12.6562H20.8125M24.8906 18H20.0391L24.3984 7.59375H29.25"
          fill="white"
        />
        <path
          d="M21.3047 24.3281C22.7803 24.3281 23.9766 23.1319 23.9766 21.6562C23.9766 20.1806 22.7803 18.9844 21.3047 18.9844C19.8291 18.9844 18.6328 20.1806 18.6328 21.6562C18.6328 23.1319 19.8291 24.3281 21.3047 24.3281Z"
          fill="white"
        />
      </g>
      <defs>
        <clipPath id="clip0_3524_729">
          <rect width="36" height="36" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
