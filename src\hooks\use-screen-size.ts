import { useCallback, useEffect, useState } from "react";

interface ScreenSizeType {
  width: number;
  height: number;
}

export default function useScreenSize() {
  const [screenSize, setScreenSize] = useState<ScreenSizeType>({
    width: 0,
    height: 0,
  });

  const updateScreenSize = useCallback(() => {
    setScreenSize({
      width: window.innerWidth,
      height: innerHeight,
    });
  }, []);

  useEffect(() => {
    if (typeof window !== "undefined") {
      updateScreenSize();

      window.addEventListener("resize", updateScreenSize);
    }

    return () => {
      window.removeEventListener("resize", updateScreenSize);
    };
  }, []);

  return screenSize;
}

export function useProductSwiper(productCount: number) {
  const { width } = useScreenSize();
  const isMobile = width < 768;

  const shouldUseSwiper = isMobile ? productCount > 1 : productCount > 3;

  return {
    isMobile,
    shouldUseSwiper,
    showPromotionalContainer: !isMobile,
    isSingleProduct: productCount === 1,
  };
}
